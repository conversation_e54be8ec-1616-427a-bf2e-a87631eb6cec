package com.maersk.sd1.sdy.service;

import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sdy.dto.PendingPlanningInstructionInput;
import com.maersk.sd1.sdy.dto.PendingPlanningInstructionOutput;
import com.maersk.sd1.sdy.dto.UpdateLocationInputDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Tests focused on data mapping and complex scenarios
 */
@ExtendWith(MockitoExtension.class)
class PendingPlanningInstructionServiceMappingTest {

    @Mock
    private CatalogRepository catalogRepository;
    
    @Mock
    private BusinessUnitRepository businessUnitRepository;
    
    @Mock
    private YardRepository yardRepository;
    
    @Mock
    private MovementInstructionRepository movementInstructionRepository;
    
    @Mock
    private EirRepository eirRepository;
    
    @Mock
    private ContainerRestrictionRepository containerRestrictionRepository;
    
    @Mock
    private ContainerPreassignmentRepository containerPreassignmentRepository;

    @InjectMocks
    private PendingPlanningInstructionService service;

    private PendingPlanningInstructionInput.Input input;

    @BeforeEach
    void setUp() {
        input = new PendingPlanningInstructionInput.Input();
        input.setLocalBusinessUnitId(1);
        input.setContainerNumber("TEST123");
    }

    @Test
    void testLocationMapping_With20ftContainer() {
        // Arrange
        setupBasicMocks();
        List<Object[]> detailedMovementData = Arrays.asList(
            new Object[]{
                1, 100, "LOAD", 1, "Pending", true,
                "ABC123", "Test Company", 1, "RES001", "Resource 1", "CHASSIS123",
                1, "TEST123", "20", "DRY", "A", 1, "GP", 1, 200,
                "ORIGIN", "BLK001", "Block 1", "A", "01", 1, // origin 20ft
                null, null, null, null, null, // origin 40ft (nulls)
                "DEST", "BLK002", "Block 2", "B", "02", 2, // dest 20ft
                null, null, null, null, null, // dest 40ft (nulls)
                1, "QUEUE001", "Queue 1", 1,
                1, "YARD001", "Test Yard",
                100, 1, "Empty", 1, 1, 1, "DOC123"
            }
        );
        when(movementInstructionRepository.findDetailedMovementInstructionData(anyList(), anyInt()))
            .thenReturn(detailedMovementData);

        // Act
        List<PendingPlanningInstructionOutput> result = service.getPendingPlanningInstructions(input);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        
        PendingPlanningInstructionOutput output = result.get(0);
        
        // Verify initial location (origin)
        UpdateLocationInputDTO.ContainerLocationValue initialLocation = output.getInitialLocation();
        assertNotNull(initialLocation);
        assertEquals("BLK001", initialLocation.getBlockCode());
        assertEquals("Block 1", initialLocation.getBlockName());
        assertEquals("A", initialLocation.getRow());
        assertEquals("01", initialLocation.getColumn());
        assertEquals(1, initialLocation.getLevel());
        
        // Verify final location (destination)
        UpdateLocationInputDTO.ContainerLocationValue finalLocation = output.getFinalLocation();
        assertNotNull(finalLocation);
        assertEquals("BLK002", finalLocation.getBlockCode());
        assertEquals("Block 2", finalLocation.getBlockName());
        assertEquals("B", finalLocation.getRow());
        assertEquals("02", finalLocation.getColumn());
        assertEquals(2, finalLocation.getLevel());
    }

    @Test
    void testLocationMapping_With40ftContainer() {
        // Arrange
        setupBasicMocks();
        List<Object[]> detailedMovementData = Arrays.asList(
            new Object[]{
                1, 100, "LOAD", 1, "Pending", true,
                "ABC123", "Test Company", 1, "RES001", "Resource 1", "CHASSIS123",
                1, "TEST123", "40", "DRY", "A", 1, "GP", 1, 200,
                "ORIGIN", null, null, null, null, null, // origin 20ft (nulls)
                "BLK001_40", "Block 1 40", "A", "01", 1, // origin 40ft
                "DEST", null, null, null, null, null, // dest 20ft (nulls)
                "BLK002_40", "Block 2 40", "B", "02", 2, // dest 40ft
                1, "QUEUE001", "Queue 1", 1,
                1, "YARD001", "Test Yard",
                100, 1, "Empty", 1, 1, 1, "DOC123"
            }
        );
        when(movementInstructionRepository.findDetailedMovementInstructionData(anyList(), anyInt()))
            .thenReturn(detailedMovementData);

        // Act
        List<PendingPlanningInstructionOutput> result = service.getPendingPlanningInstructions(input);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        
        PendingPlanningInstructionOutput output = result.get(0);
        
        // Verify initial location uses 40ft data
        UpdateLocationInputDTO.ContainerLocationValue initialLocation = output.getInitialLocation();
        assertNotNull(initialLocation);
        assertEquals("BLK001_40", initialLocation.getBlockCode());
        assertEquals("Block 1 40", initialLocation.getBlockName());
        
        // Verify final location uses 40ft data
        UpdateLocationInputDTO.ContainerLocationValue finalLocation = output.getFinalLocation();
        assertNotNull(finalLocation);
        assertEquals("BLK002_40", finalLocation.getBlockCode());
        assertEquals("Block 2 40", finalLocation.getBlockName());
    }

    @Test
    void testVehicleAndChassisMapping_WithReferenceEir() {
        // Arrange
        setupBasicMocks();
        List<Object[]> detailedMovementData = Arrays.asList(
            new Object[]{
                1, 100, "LOAD", 1, "Pending", true,
                "REF_VEHICLE", "Reference Company", 1, "RES001", "Resource 1", "REF_CHASSIS", // reference EIR data
                1, "TEST123", "20", "DRY", "A", 1, "GP", 1, 200,
                "ORIGIN", "BLK001", "Block 1", "A", "01", 1,
                null, null, null, null, null,
                "DEST", "BLK002", "Block 2", "B", "02", 2,
                null, null, null, null, null,
                1, "QUEUE001", "Queue 1", 1,
                1, "YARD001", "Test Yard",
                100, 1, "Empty", 1, 1, 1, "DOC123"
            }
        );
        when(movementInstructionRepository.findDetailedMovementInstructionData(anyList(), anyInt()))
            .thenReturn(detailedMovementData);

        // Act
        List<PendingPlanningInstructionOutput> result = service.getPendingPlanningInstructions(input);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        
        PendingPlanningInstructionOutput output = result.get(0);
        assertEquals("REF_VEHICLE", output.getVehiclePlate());
        assertEquals("Reference Company", output.getVehicleCompany());
        assertEquals("REF_CHASSIS", output.getChassisNumber());
    }

    @Test
    void testContainerDataMapping_AllFields() {
        // Arrange
        setupBasicMocks();
        List<Object[]> detailedMovementData = Arrays.asList(
            new Object[]{
                1, 100, "LOAD", 1, "Pending", true,
                "ABC123", "Test Company", 1, "RES001", "Resource 1", "CHASSIS123",
                1, "TEST123", "20", "DRY", "Grade A", 1, "General Purpose", 1, 200,
                "ORIGIN", "BLK001", "Block 1", "A", "01", 1,
                null, null, null, null, null,
                "DEST", "BLK002", "Block 2", "B", "02", 2,
                null, null, null, null, null,
                1, "QUEUE001", "Queue 1", 1,
                1, "YARD001", "Test Yard",
                100, 2, "Full", 1, 1, 1, "DOC123"
            }
        );
        when(movementInstructionRepository.findDetailedMovementInstructionData(anyList(), anyInt()))
            .thenReturn(detailedMovementData);

        // Act
        List<PendingPlanningInstructionOutput> result = service.getPendingPlanningInstructions(input);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        
        PendingPlanningInstructionOutput output = result.get(0);
        assertEquals("TEST123", output.getContainerNumber());
        assertEquals("DRY", output.getFamilyDescription());
        assertEquals("Grade A", output.getClassDescription());
        assertEquals("General Purpose", output.getContainerTypeDescription());
        assertEquals(1, output.getClassCategoryId());
    }

    @Test
    void testWorkQueueAndYardMapping() {
        // Arrange
        setupBasicMocks();
        List<Object[]> detailedMovementData = Arrays.asList(
            new Object[]{
                1, 100, "LOAD", 1, "Pending", true,
                "ABC123", "Test Company", 1, "RES001", "Resource 1", "CHASSIS123",
                1, "TEST123", "20", "DRY", "A", 1, "GP", 1, 200,
                "ORIGIN", "BLK001", "Block 1", "A", "01", 1,
                null, null, null, null, null,
                "DEST", "BLK002", "Block 2", "B", "02", 2,
                null, null, null, null, null,
                5, "LOAD_QUEUE", "Loading Queue", 10, // work queue data
                3, "MAIN_YARD", "Main Terminal Yard", // yard data
                100, 1, "Empty", 1, 1, 1, "BOOKING123"
            }
        );
        when(movementInstructionRepository.findDetailedMovementInstructionData(anyList(), anyInt()))
            .thenReturn(detailedMovementData);

        // Act
        List<PendingPlanningInstructionOutput> result = service.getPendingPlanningInstructions(input);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        
        PendingPlanningInstructionOutput output = result.get(0);
        assertEquals("LOAD_QUEUE", output.getQueueCode());
        assertEquals("Loading Queue", output.getQueueDescription());
        assertEquals(10, output.getQueueResourceId());
        assertEquals("MAIN_YARD", output.getYardCode());
        assertEquals("Main Terminal Yard", output.getYardName());
        assertEquals("BOOKING123", output.getDocumentNumber());
    }

    @Test
    void testSortingLogic_MultipleRecords() {
        // Arrange
        setupBasicMocks();
        List<Object[]> detailedMovementData = Arrays.asList(
            // Second record (higher queue resource ID)
            new Object[]{
                2, 102, "DISCHARGE", 2, "Pending", false,
                "DEF456", "Company B", 2, "RES002", "Resource 2", "CHASSIS456",
                2, "TEST456", "40", "REEFER", "B", 2, "RF", 2, 202,
                "ORIGIN", "BLK003", "Block 3", "C", "03", 3,
                null, null, null, null, null,
                "DEST", "BLK004", "Block 4", "D", "04", 4,
                null, null, null, null, null,
                2, "QUEUE002", "Queue 2", 20, // higher queue resource ID
                2, "YARD002", "Yard 2",
                102, 2, "Full", 2, 2, 2, "DOC456"
            },
            // First record (lower queue resource ID)
            new Object[]{
                1, 101, "LOAD", 1, "Pending", true,
                "ABC123", "Company A", 1, "RES001", "Resource 1", "CHASSIS123",
                1, "TEST123", "20", "DRY", "A", 1, "GP", 1, 201,
                "ORIGIN", "BLK001", "Block 1", "A", "01", 1,
                null, null, null, null, null,
                "DEST", "BLK002", "Block 2", "B", "02", 2,
                null, null, null, null, null,
                1, "QUEUE001", "Queue 1", 10, // lower queue resource ID
                1, "YARD001", "Yard 1",
                101, 1, "Empty", 1, 1, 1, "DOC123"
            },
            // Third record (same queue resource ID as first, but higher sequence)
            new Object[]{
                3, 103, "LOAD", 3, "Pending", true,
                "GHI789", "Company C", 3, "RES003", "Resource 3", "CHASSIS789",
                3, "TEST789", "20", "DRY", "C", 3, "GP", 3, 203,
                "ORIGIN", "BLK005", "Block 5", "E", "05", 5,
                null, null, null, null, null,
                "DEST", "BLK006", "Block 6", "F", "06", 6,
                null, null, null, null, null,
                3, "QUEUE003", "Queue 3", 10, // same queue resource ID as first
                3, "YARD003", "Yard 3",
                103, 3, "Empty", 3, 3, 3, "DOC789"
            }
        );
        when(movementInstructionRepository.findDetailedMovementInstructionData(anyList(), anyInt()))
            .thenReturn(detailedMovementData);

        // Act
        List<PendingPlanningInstructionOutput> result = service.getPendingPlanningInstructions(input);

        // Assert
        assertNotNull(result);
        assertEquals(3, result.size());
        
        // Verify sorting: first by queue resource ID, then by sequence
        assertEquals(10, result.get(0).getQueueResourceId()); // First: queue resource 10, sequence 1
        assertEquals(1, result.get(0).getSequence());
        
        assertEquals(10, result.get(1).getQueueResourceId()); // Second: queue resource 10, sequence 3
        assertEquals(3, result.get(1).getSequence());
        
        assertEquals(20, result.get(2).getQueueResourceId()); // Third: queue resource 20, sequence 2
        assertEquals(2, result.get(2).getSequence());
    }

    @Test
    void testErrorHandling_InvalidDetailData() {
        // Arrange
        setupBasicMocks();
        List<Object[]> detailedMovementData = Arrays.asList(
            new Object[]{1, 2, 3} // Invalid data with too few fields
        );
        when(movementInstructionRepository.findDetailedMovementInstructionData(anyList(), anyInt()))
            .thenReturn(detailedMovementData);

        // Act
        List<PendingPlanningInstructionOutput> result = service.getPendingPlanningInstructions(input);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty()); // Should handle invalid data gracefully
    }

    @Test
    void testNullHandling_InDetailData() {
        // Arrange
        setupBasicMocks();
        List<Object[]> detailedMovementData = Arrays.asList(
            new Object[]{
                null, null, null, null, null, null, // nulls for basic data
                null, null, null, null, null, null, // nulls for vehicle/resource data
                1, "TEST123", null, null, null, null, null, null, null, // container data with nulls
                null, null, null, null, null, null, // origin location nulls
                null, null, null, null, null, // origin 40ft nulls
                null, null, null, null, null, null, // dest location nulls
                null, null, null, null, null, // dest 40ft nulls
                null, null, null, null, // work queue nulls
                null, null, null, // yard nulls
                null, null, null, null, null, null, null // additional fields nulls
            }
        );
        when(movementInstructionRepository.findDetailedMovementInstructionData(anyList(), anyInt()))
            .thenReturn(detailedMovementData);

        // Act
        List<PendingPlanningInstructionOutput> result = service.getPendingPlanningInstructions(input);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        
        PendingPlanningInstructionOutput output = result.get(0);
        assertNull(output.getSequence());
        assertNull(output.getMovementInstructionId());
        assertEquals("TEST123", output.getContainerNumber()); // Only non-null field
        assertNull(output.getVehiclePlate());
        assertNull(output.getChassisNumber());
    }

    private void setupBasicMocks() {
        // Setup catalog data
        List<Object[]> catalogResults = Arrays.asList(
            new Object[]{"sd1_equipment_category_container", 48752},
            new Object[]{"43081", 43081},
            new Object[]{"43083", 43083}
        );
        when(catalogRepository.findIdsByAliases(anyList())).thenReturn(catalogResults);

        List<Integer> statusIdResults = Arrays.asList(1, 2, 3);
        when(catalogRepository.findStatusIdsByParentCodeAndCodes(anyString(), anyList()))
            .thenReturn(statusIdResults);

        // Setup business unit data
        when(businessUnitRepository.findParentBusinessUnitIdByBusinessUnitId(1)).thenReturn(10);
        when(yardRepository.findYardIdByBusinessUnitId(1)).thenReturn(100);

        // Setup movement instruction data
        List<PendingPlanningInstructionService.PendingMovementInstructionDTO> pendingInstructions = Arrays.asList(
            new PendingPlanningInstructionService.PendingMovementInstructionDTO(100, 1, "TEST123")
        );
        when(movementInstructionRepository.findPendingMovementInstructions(anyList(), anyString(), anyInt()))
            .thenReturn(pendingInstructions);

        // Setup container info data
        List<PendingPlanningInstructionService.ContainerInfoDTO> containerInfo = Arrays.asList(
            new PendingPlanningInstructionService.ContainerInfoDTO(
                1, "TEST123", 1, "20", 1, "DRY", "A", 1, "GP", 1, 200, 1, null, null
            )
        );
        when(movementInstructionRepository.findContainerInfoByIds(anyList(), anyInt()))
            .thenReturn(containerInfo);

        // Setup other mock data
        when(eirRepository.findEmptyFullIdsByEirIds(anyList())).thenReturn(Collections.emptyList());
        when(containerRestrictionRepository.findRestrictionsByContainerIdsSubBusinessUnitIdsAndCatEmptyFullIds(
            anySet(), anySet(), anySet())).thenReturn(Collections.emptyList());
        when(containerPreassignmentRepository.findLatestPreallocationBookingNumbers(
            anyList(), anyInt(), anyInt(), anyInt())).thenReturn(Collections.emptyList());

        List<Object[]> movementInfoResults = Arrays.asList(
            new Object[]{1, 100, 1, 200}
        );
        when(movementInstructionRepository.findMovementInformationWithWorkQueueResources(anyList()))
            .thenReturn(movementInfoResults);

        when(movementInstructionRepository.findLatestReferenceEirIds(anyList()))
            .thenReturn(Collections.emptyList());
    }
}
