package com.maersk.sd1.sdy.service;

import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sdy.dto.PendingPlanningInstructionInput;
import com.maersk.sd1.sdy.dto.PendingPlanningInstructionOutput;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration tests for PendingPlanningInstructionService
 * These tests require a test database with actual data
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
class PendingPlanningInstructionServiceIntegrationTest {

    @Autowired
    private PendingPlanningInstructionService service;

    @Test
    void testGetPendingPlanningInstructions_WithRealData() {
        // Arrange
        PendingPlanningInstructionInput.Input input = new PendingPlanningInstructionInput.Input();
        input.setLocalBusinessUnitId(1);
        input.setContainerNumber("TEST");

        // Act
        List<PendingPlanningInstructionOutput> result = service.getPendingPlanningInstructions(input);

        // Assert
        assertNotNull(result);
        // Additional assertions based on your test data
    }

    @Test
    void testGetPendingPlanningInstructions_WithNonExistentContainer() {
        // Arrange
        PendingPlanningInstructionInput.Input input = new PendingPlanningInstructionInput.Input();
        input.setLocalBusinessUnitId(1);
        input.setContainerNumber("NONEXISTENT");

        // Act
        List<PendingPlanningInstructionOutput> result = service.getPendingPlanningInstructions(input);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetPendingPlanningInstructions_WithInvalidBusinessUnit() {
        // Arrange
        PendingPlanningInstructionInput.Input input = new PendingPlanningInstructionInput.Input();
        input.setLocalBusinessUnitId(99999);
        input.setContainerNumber("TEST");

        // Act & Assert
        assertDoesNotThrow(() -> {
            List<PendingPlanningInstructionOutput> result = service.getPendingPlanningInstructions(input);
            assertNotNull(result);
        });
    }
}
