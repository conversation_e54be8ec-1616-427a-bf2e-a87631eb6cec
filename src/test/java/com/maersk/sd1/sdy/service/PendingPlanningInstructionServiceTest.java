package com.maersk.sd1.sdy.service;

import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sdy.dto.PendingPlanningInstructionInput;
import com.maersk.sd1.sdy.dto.PendingPlanningInstructionOutput;
import com.maersk.sd1.sdy.dto.UpdateLocationInputDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class PendingPlanningInstructionServiceTest {

    @Mock
    private CatalogRepository catalogRepository;
    
    @Mock
    private BusinessUnitRepository businessUnitRepository;
    
    @Mock
    private YardRepository yardRepository;
    
    @Mock
    private MovementInstructionRepository movementInstructionRepository;
    
    @Mock
    private EirRepository eirRepository;
    
    @Mock
    private ContainerRestrictionRepository containerRestrictionRepository;
    
    @Mock
    private ContainerPreassignmentRepository containerPreassignmentRepository;

    @InjectMocks
    private PendingPlanningInstructionService service;

    private PendingPlanningInstructionInput.Input input;
    private List<Integer> statusIds;
    private Map<String, Integer> catalogIds;

    @BeforeEach
    void setUp() {
        input = new PendingPlanningInstructionInput.Input();
        input.setLocalBusinessUnitId(1);
        input.setContainerNumber("TEST123");

        statusIds = Arrays.asList(1, 2, 3);
        catalogIds = Map.of(
            "isGateOut", 43083,
            "isEmpty", 43081,
            "isContainer", 48752
        );
    }

    @Test
    void testGetPendingPlanningInstructions_Success() {
        // Arrange
        setupMockCatalogData();
        setupMockBusinessUnitData();
        setupMockMovementInstructionData();
        setupMockContainerInfoData();
        setupMockEirData();
        setupMockRestrictionData();
        setupMockPreallocationData();
        setupMockMovementInformationData();
        setupMockDetailedMovementData();

        // Act
        List<PendingPlanningInstructionOutput> result = service.getPendingPlanningInstructions(input);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        
        PendingPlanningInstructionOutput output = result.get(0);
        assertEquals(1, output.getSequence());
        assertEquals(100, output.getMovementInstructionId());
        assertEquals("TEST123", output.getContainerNumber());
        assertEquals("LOAD", output.getMovementType());
        assertEquals(1, output.getWorkQueueId());
        assertTrue(output.getRequiresTruck());
        assertEquals("ABC123", output.getVehiclePlate());
        assertEquals("Test Company", output.getVehicleCompany());
        assertEquals("CHASSIS123", output.getChassisNumber());
        assertEquals("RES001", output.getQueueResourceAlias());
        assertEquals("Resource 1", output.getQueueResourceName());
        
        // Verify location data
        assertNotNull(output.getInitialLocation());
        assertEquals("BLK001", output.getInitialLocation().getBlockCode());
        assertEquals("Block 1", output.getInitialLocation().getBlockName());
        assertEquals("A", output.getInitialLocation().getRow());
        assertEquals("01", output.getInitialLocation().getColumn());
        assertEquals(1, output.getInitialLocation().getLevel());
        
        assertNotNull(output.getFinalLocation());
        assertEquals("BLK002", output.getFinalLocation().getBlockCode());
        assertEquals("Block 2", output.getFinalLocation().getBlockName());
        assertEquals("B", output.getFinalLocation().getRow());
        assertEquals("02", output.getFinalLocation().getColumn());
        assertEquals(2, output.getFinalLocation().getLevel());
        
        // Verify yard data
        assertEquals("YARD001", output.getYardCode());
        assertEquals("Test Yard", output.getYardName());
        
        // Verify container data
        assertEquals("20", output.getContainerSize());
        assertEquals("DRY", output.getFamilyDescription());
        assertEquals("A", output.getClassDescription());
        assertEquals("GP", output.getContainerTypeDescription());
        assertEquals("Good", output.getContainerCondition());
        
        // Verify document data
        assertEquals("DOC123", output.getDocumentNumber());
    }

    @Test
    void testGetPendingPlanningInstructions_NoMovementInstructions() {
        // Arrange
        setupMockCatalogData();
        setupMockBusinessUnitData();
        when(movementInstructionRepository.findPendingMovementInstructions(anyList(), anyString(), anyInt()))
            .thenReturn(Collections.emptyList());

        // Act
        List<PendingPlanningInstructionOutput> result = service.getPendingPlanningInstructions(input);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // Verify that container info methods are not called when no movement instructions
        verify(movementInstructionRepository, never()).findContainerInfoByIds(anyList(), anyInt());
        verify(containerRestrictionRepository, never()).findRestrictionsByContainerIdsSubBusinessUnitIdsAndCatEmptyFullIds(anySet(), anySet(), anySet());
        verify(containerPreassignmentRepository, never()).findLatestPreallocationBookingNumbers(anyList(), anyInt(), anyInt(), anyInt());
    }

    @Test
    void testGetPendingPlanningInstructions_ExceptionHandling() {
        // Arrange
        when(catalogRepository.findIdsByAliases(anyList()))
            .thenThrow(new RuntimeException("Database error"));

        // Act
        List<PendingPlanningInstructionOutput> result = service.getPendingPlanningInstructions(input);

        // Assert
        assertNull(result);
    }

    @Test
    void testUpdateContainerInfoWithRestrictionReasons_Success() {
        // Arrange
        List<PendingPlanningInstructionService.ContainerInfoDTO> containerInfo = createTestContainerInfo();
        setupMockRestrictionData();

        // Act
        service.getPendingPlanningInstructions(input); // This will call the private method internally

        // Assert
        verify(containerRestrictionRepository).findRestrictionsByContainerIdsSubBusinessUnitIdsAndCatEmptyFullIds(
            anySet(), anySet(), anySet());
    }

    @Test
    void testUpdateContainerInfoWithLastPreallocationBookingNumber_Success() {
        // Arrange
        List<PendingPlanningInstructionService.ContainerInfoDTO> containerInfo = createTestContainerInfo();
        setupMockPreallocationData();

        // Act
        service.getPendingPlanningInstructions(input); // This will call the private method internally

        // Assert
        verify(containerPreassignmentRepository).findLatestPreallocationBookingNumbers(
            anyList(), anyInt(), anyInt(), anyInt());
    }

    @Test
    void testCreateMovementsInformation_Success() {
        // Arrange
        setupMockCatalogData();
        setupMockBusinessUnitData();
        setupMockMovementInstructionData();
        setupMockContainerInfoData();
        setupMockEirData();
        setupMockRestrictionData();
        setupMockPreallocationData();
        setupMockMovementInformationData();
        setupMockDetailedMovementData();

        // Act
        List<PendingPlanningInstructionOutput> result = service.getPendingPlanningInstructions(input);

        // Assert
        verify(movementInstructionRepository).findMovementInformationWithWorkQueueResources(anyList());
    }

    @Test
    void testUpdateMovementsInformationWithReferenceEirIds_Success() {
        // Arrange
        setupMockCatalogData();
        setupMockBusinessUnitData();
        setupMockMovementInstructionData();
        setupMockContainerInfoData();
        setupMockEirData();
        setupMockRestrictionData();
        setupMockPreallocationData();
        setupMockMovementInformationData();
        setupMockDetailedMovementData();

        List<Object[]> referenceEirResults = Arrays.asList(
            new Object[]{100, 200}
        );
        when(movementInstructionRepository.findLatestReferenceEirIds(anyList()))
            .thenReturn(referenceEirResults);

        // Act
        List<PendingPlanningInstructionOutput> result = service.getPendingPlanningInstructions(input);

        // Assert
        verify(movementInstructionRepository).findLatestReferenceEirIds(anyList());
    }

    @Test
    void testBuildFinalResultSet_EmptyData() {
        // Arrange
        setupMockCatalogData();
        setupMockBusinessUnitData();
        when(movementInstructionRepository.findPendingMovementInstructions(anyList(), anyString(), anyInt()))
            .thenReturn(Collections.emptyList());

        // Act
        List<PendingPlanningInstructionOutput> result = service.getPendingPlanningInstructions(input);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testSortingByWorkQueueResourceIdAndSequence() {
        // Arrange
        setupMockCatalogData();
        setupMockBusinessUnitData();
        setupMockMovementInstructionData();
        setupMockContainerInfoData();
        setupMockEirData();
        setupMockRestrictionData();
        setupMockPreallocationData();
        setupMockMovementInformationData();
        setupMockMultipleDetailedMovementData(); // Multiple records for sorting test

        // Act
        List<PendingPlanningInstructionOutput> result = service.getPendingPlanningInstructions(input);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        
        // Verify sorting: first by work_queue_resource_id, then by sequence
        assertTrue(result.get(0).getQueueResourceId() <= result.get(1).getQueueResourceId());
        if (Objects.equals(result.get(0).getQueueResourceId(), result.get(1).getQueueResourceId())) {
            assertTrue(result.get(0).getSequence() <= result.get(1).getSequence());
        }
    }

    @Test
    void testNullHandlingInMapping() {
        // Arrange
        setupMockCatalogData();
        setupMockBusinessUnitData();
        setupMockMovementInstructionData();
        setupMockContainerInfoData();
        setupMockEirData();
        setupMockRestrictionData();
        setupMockPreallocationData();
        setupMockMovementInformationData();
        
        // Setup detailed movement data with null values
        List<Object[]> detailedMovementData = Arrays.asList(
            new Object[]{
                1, 100, "LOAD", 1, "Pending", true,
                null, null, 1, "RES001", "Resource 1", null, // nulls for vehicle/chassis
                1, "TEST123", "20", "DRY", "A", 1, "GP", 1, 200, // container data
                "BLK001", "BLK001", "Block 1", "A", "01", 1, // origin 20ft
                null, null, null, null, null, // origin 40ft (nulls)
                "BLK002", "BLK002", "Block 2", "B", "02", 2, // dest 20ft
                null, null, null, null, null, // dest 40ft (nulls)
                1, "QUEUE001", "Queue 1", 1, // work queue
                1, "YARD001", "Test Yard", // yard
                100, 1, "Empty", 1, 1, 1, "DOC123" // additional fields
            }
        );
        when(movementInstructionRepository.findDetailedMovementInstructionData(anyList(), anyInt()))
            .thenReturn(detailedMovementData);

        // Act
        List<PendingPlanningInstructionOutput> result = service.getPendingPlanningInstructions(input);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        
        PendingPlanningInstructionOutput output = result.get(0);
        assertNull(output.getVehiclePlate());
        assertNull(output.getVehicleCompany());
        assertNull(output.getChassisNumber());
    }

    // Helper methods to setup mock data

    private void setupMockCatalogData() {
        List<Object[]> catalogResults = Arrays.asList(
            new Object[]{"sd1_equipment_category_container", 48752},
            new Object[]{"43081", 43081},
            new Object[]{"43083", 43083}
        );
        when(catalogRepository.findIdsByAliases(anyList())).thenReturn(catalogResults);

        List<Integer> statusIdResults = Arrays.asList(1, 2, 3);
        when(catalogRepository.findStatusIdsByParentCodeAndCodes(anyString(), anyList()))
            .thenReturn(statusIdResults);
    }

    private void setupMockBusinessUnitData() {
        when(businessUnitRepository.findParentBusinessUnitIdByBusinessUnitId(1)).thenReturn(10);
        when(yardRepository.findYardIdByBusinessUnitId(1)).thenReturn(100);
    }

    private void setupMockMovementInstructionData() {
        List<PendingPlanningInstructionService.PendingMovementInstructionDTO> pendingInstructions = Arrays.asList(
            new PendingPlanningInstructionService.PendingMovementInstructionDTO(100, 1, "TEST123")
        );
        when(movementInstructionRepository.findPendingMovementInstructions(anyList(), anyString(), anyInt()))
            .thenReturn(pendingInstructions);
    }

    private void setupMockContainerInfoData() {
        List<PendingPlanningInstructionService.ContainerInfoDTO> containerInfo = createTestContainerInfo();
        when(movementInstructionRepository.findContainerInfoByIds(anyList(), anyInt()))
            .thenReturn(containerInfo);
    }

    private void setupMockEirData() {
        List<Object[]> eirResults = Arrays.asList(
            new Object[]{200, 1}
        );
        when(eirRepository.findEmptyFullIdsByEirIds(anyList())).thenReturn(eirResults);
    }

    private void setupMockRestrictionData() {
        List<Object[]> restrictionResults = Arrays.asList(
            new Object[]{1, 1, 1, "Test restriction"}
        );
        when(containerRestrictionRepository.findRestrictionsByContainerIdsSubBusinessUnitIdsAndCatEmptyFullIds(
            anySet(), anySet(), anySet())).thenReturn(restrictionResults);
    }

    private void setupMockPreallocationData() {
        List<Object[]> preallocationResults = Arrays.asList(
            new Object[]{1, "BOOK123"}
        );
        when(containerPreassignmentRepository.findLatestPreallocationBookingNumbers(
            anyList(), anyInt(), anyInt(), anyInt())).thenReturn(preallocationResults);
    }

    private void setupMockMovementInformationData() {
        List<Object[]> movementInfoResults = Arrays.asList(
            new Object[]{1, 100, 1, 200}
        );
        when(movementInstructionRepository.findMovementInformationWithWorkQueueResources(anyList()))
            .thenReturn(movementInfoResults);
    }

    private void setupMockDetailedMovementData() {
        List<Object[]> detailedMovementData = Arrays.asList(
            new Object[]{
                1, 100, "LOAD", 1, "Pending", true,
                "ABC123", "Test Company", 1, "RES001", "Resource 1", "CHASSIS123",
                1, "TEST123", "20", "DRY", "A", 1, "GP", 1, 200,
                "BLK001", "BLK001", "Block 1", "A", "01", 1,
                "BLK001_40", "Block 1 40", "A", "01", 1,
                "BLK002", "BLK002", "Block 2", "B", "02", 2,
                "BLK002_40", "Block 2 40", "B", "02", 2,
                1, "QUEUE001", "Queue 1", 1,
                1, "YARD001", "Test Yard",
                100, 1, "Empty", 1, 1, 1, "DOC123"
            }
        );
        when(movementInstructionRepository.findDetailedMovementInstructionData(anyList(), anyInt()))
            .thenReturn(detailedMovementData);
    }

    private void setupMockMultipleDetailedMovementData() {
        List<Object[]> detailedMovementData = Arrays.asList(
            new Object[]{
                2, 101, "LOAD", 1, "Pending", true,
                "ABC124", "Test Company", 1, "RES001", "Resource 1", "CHASSIS124",
                2, "TEST124", "20", "DRY", "A", 1, "GP", 1, 201,
                "BLK001", "BLK001", "Block 1", "A", "01", 1,
                "BLK001_40", "Block 1 40", "A", "01", 1,
                "BLK002", "BLK002", "Block 2", "B", "02", 2,
                "BLK002_40", "Block 2 40", "B", "02", 2,
                1, "QUEUE001", "Queue 1", 2,
                1, "YARD001", "Test Yard",
                101, 1, "Empty", 1, 1, 1, "DOC124"
            },
            new Object[]{
                1, 100, "LOAD", 1, "Pending", true,
                "ABC123", "Test Company", 1, "RES001", "Resource 1", "CHASSIS123",
                1, "TEST123", "20", "DRY", "A", 1, "GP", 1, 200,
                "BLK001", "BLK001", "Block 1", "A", "01", 1,
                "BLK001_40", "Block 1 40", "A", "01", 1,
                "BLK002", "BLK002", "Block 2", "B", "02", 2,
                "BLK002_40", "Block 2 40", "B", "02", 2,
                1, "QUEUE001", "Queue 1", 1,
                1, "YARD001", "Test Yard",
                100, 1, "Empty", 1, 1, 1, "DOC123"
            }
        );
        when(movementInstructionRepository.findDetailedMovementInstructionData(anyList(), anyInt()))
            .thenReturn(detailedMovementData);
    }

    private List<PendingPlanningInstructionService.ContainerInfoDTO> createTestContainerInfo() {
        return Arrays.asList(
            new PendingPlanningInstructionService.ContainerInfoDTO(
                1, "TEST123", 1, "20", 1, "DRY", "A", 1, "GP", 1, 200, 1, null, null
            )
        );
    }
}
