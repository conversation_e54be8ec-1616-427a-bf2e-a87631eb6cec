package com.maersk.sd1.sdy.service;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Tests for the DTO classes within PendingPlanningInstructionService
 */
class PendingPlanningInstructionServiceDTOTest {

    @Test
    void testPendingMovementInstructionDTO_Creation() {
        // Arrange & Act
        PendingPlanningInstructionService.PendingMovementInstructionDTO dto = 
            new PendingPlanningInstructionService.PendingMovementInstructionDTO(100, 1, "TEST123");

        // Assert
        assertEquals(100, dto.getMovementInstructionId());
        assertEquals(1, dto.getContainerId());
        assertEquals("TEST123", dto.getContainerNumber());
    }

    @Test
    void testPendingMovementInstructionDTO_SettersAndGetters() {
        // Arrange
        PendingPlanningInstructionService.PendingMovementInstructionDTO dto = 
            new PendingPlanningInstructionService.PendingMovementInstructionDTO(100, 1, "TEST123");

        // Act
        dto.setMovementInstructionId(200);
        dto.setContainerId(2);
        dto.setContainerNumber("TEST456");

        // Assert
        assertEquals(200, dto.getMovementInstructionId());
        assertEquals(2, dto.getContainerId());
        assertEquals("TEST456", dto.getContainerNumber());
    }

    @Test
    void testContainerInfoDTO_Creation() {
        // Arrange & Act
        PendingPlanningInstructionService.ContainerInfoDTO dto = 
            new PendingPlanningInstructionService.ContainerInfoDTO(
                1, "TEST123", 1, "20", 1, "DRY", "A", 1, "GP", 1, 200, 1, "Test restriction", "BOOK123"
            );

        // Assert
        assertEquals(1, dto.getContainerId());
        assertEquals("TEST123", dto.getContainerNumber());
        assertEquals(1, dto.getContainerSizeId());
        assertEquals("20", dto.getContainerSizeCode());
        assertEquals(1, dto.getContainerFamilyId());
        assertEquals("DRY", dto.getContainerFamilyDescription());
        assertEquals("A", dto.getContainerGradeDescription());
        assertEquals(1, dto.getContainerTypeId());
        assertEquals("GP", dto.getContainerTypeDescription());
        assertEquals(1, dto.getContainerGradeId());
        assertEquals(200, dto.getLastGateInEirId());
        assertEquals(1, dto.getCatEmptyFullId());
        assertEquals("Test restriction", dto.getRestrictionReasons());
        assertEquals("BOOK123", dto.getLastPreallocationBookingNumber());
    }

    @Test
    void testContainerInfoDTO_SettersAndGetters() {
        // Arrange
        PendingPlanningInstructionService.ContainerInfoDTO dto = 
            new PendingPlanningInstructionService.ContainerInfoDTO(
                1, "TEST123", 1, "20", 1, "DRY", "A", 1, "GP", 1, 200, 1, null, null
            );

        // Act
        dto.setRestrictionReasons("Updated restriction");
        dto.setLastPreallocationBookingNumber("BOOK456");
        dto.setCatEmptyFullId(2);

        // Assert
        assertEquals("Updated restriction", dto.getRestrictionReasons());
        assertEquals("BOOK456", dto.getLastPreallocationBookingNumber());
        assertEquals(2, dto.getCatEmptyFullId());
    }

    @Test
    void testContainerInfoDTO_NullValues() {
        // Arrange & Act
        PendingPlanningInstructionService.ContainerInfoDTO dto = 
            new PendingPlanningInstructionService.ContainerInfoDTO(
                1, "TEST123", null, null, null, null, null, null, null, null, null, null, null, null
            );

        // Assert
        assertEquals(1, dto.getContainerId());
        assertEquals("TEST123", dto.getContainerNumber());
        assertNull(dto.getContainerSizeId());
        assertNull(dto.getContainerSizeCode());
        assertNull(dto.getContainerFamilyId());
        assertNull(dto.getContainerFamilyDescription());
        assertNull(dto.getContainerGradeDescription());
        assertNull(dto.getContainerTypeId());
        assertNull(dto.getContainerTypeDescription());
        assertNull(dto.getContainerGradeId());
        assertNull(dto.getLastGateInEirId());
        assertNull(dto.getCatEmptyFullId());
        assertNull(dto.getRestrictionReasons());
        assertNull(dto.getLastPreallocationBookingNumber());
    }

    @Test
    void testMovementInformationDTO_Creation() {
        // Arrange & Act
        PendingPlanningInstructionService.MovementInformationDTO dto = 
            new PendingPlanningInstructionService.MovementInformationDTO(1, 100, 1, 200, 300);

        // Assert
        assertEquals(1, dto.getContainerId());
        assertEquals(100, dto.getMovementInstructionId());
        assertEquals(1, dto.getWorkQueueResourceId());
        assertEquals(200, dto.getEirId());
        assertEquals(300, dto.getReferenceEirId());
    }

    @Test
    void testMovementInformationDTO_SettersAndGetters() {
        // Arrange
        PendingPlanningInstructionService.MovementInformationDTO dto = 
            new PendingPlanningInstructionService.MovementInformationDTO(1, 100, 1, 200, null);

        // Act
        dto.setReferenceEirId(400);
        dto.setEirId(250);
        dto.setWorkQueueResourceId(2);

        // Assert
        assertEquals(400, dto.getReferenceEirId());
        assertEquals(250, dto.getEirId());
        assertEquals(2, dto.getWorkQueueResourceId());
    }

    @Test
    void testMovementInformationDTO_NullValues() {
        // Arrange & Act
        PendingPlanningInstructionService.MovementInformationDTO dto = 
            new PendingPlanningInstructionService.MovementInformationDTO(null, null, null, null, null);

        // Assert
        assertNull(dto.getContainerId());
        assertNull(dto.getMovementInstructionId());
        assertNull(dto.getWorkQueueResourceId());
        assertNull(dto.getEirId());
        assertNull(dto.getReferenceEirId());
    }

    @Test
    void testDTOEquality() {
        // Arrange
        PendingPlanningInstructionService.PendingMovementInstructionDTO dto1 = 
            new PendingPlanningInstructionService.PendingMovementInstructionDTO(100, 1, "TEST123");
        PendingPlanningInstructionService.PendingMovementInstructionDTO dto2 = 
            new PendingPlanningInstructionService.PendingMovementInstructionDTO(100, 1, "TEST123");
        PendingPlanningInstructionService.PendingMovementInstructionDTO dto3 = 
            new PendingPlanningInstructionService.PendingMovementInstructionDTO(200, 1, "TEST123");

        // Assert
        assertEquals(dto1, dto2);
        assertNotEquals(dto1, dto3);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testDTOToString() {
        // Arrange
        PendingPlanningInstructionService.PendingMovementInstructionDTO dto = 
            new PendingPlanningInstructionService.PendingMovementInstructionDTO(100, 1, "TEST123");

        // Act
        String toString = dto.toString();

        // Assert
        assertNotNull(toString);
        assertTrue(toString.contains("100"));
        assertTrue(toString.contains("1"));
        assertTrue(toString.contains("TEST123"));
    }
}
