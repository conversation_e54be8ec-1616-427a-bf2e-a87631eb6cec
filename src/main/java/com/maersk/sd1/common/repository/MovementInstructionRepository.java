package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.MovementInstruction;
import com.maersk.sd1.sdy.dto.*;
import com.maersk.sd1.sdy.service.PendingPlanningInstructionService;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface MovementInstructionRepository extends JpaRepository<MovementInstruction, Integer> {
    @Modifying
    @Transactional
    @Query("UPDATE MovementInstruction m " +
            "SET m.comment = 'RETURNED', m.modificationUser.id = :userId " +
            "WHERE m.id = :movementInstructionId")
    int markMovementInstructionReturned(@Param("userId") Integer userId,
                                        @Param("movementInstructionId") Integer movementInstructionId);

    Optional<MovementInstruction> findByIdAndActive(Integer id, Boolean active);

    @Query("SELECT m FROM MovementInstruction m " +
            "WHERE m.workQueue.id = :workQueueId " +
            "  AND m.active = TRUE " +
            "  AND m.catStatus.code NOT IN ('CA','EXE')")
    List<MovementInstruction> findActiveByWorkQueueIdAndStatusNotInCAEXE(@Param("workQueueId") Integer workQueueId);


    @Query("""
    SELECT new com.maersk.sd1.sdy.dto.MovementInstructionDetailsDto(
        moi.catStatus.id,
        moi.catStatus.code,
        moi.eir.id,
        moi.catMovementType.id,
        eir.active,
        eir.truckDepartureDate,
        eir.catEmptyFull.id,
        eir.documentCargoGof.id,
        moi.container.id,
        eir.container.id,
        moi.parentMovementInstruction.id,
        moi.destinationBlock.id,
        moi.destinationCell.id,
        moi.destinationLevel.id,
        moi.destination40Block.id,
        moi.destination40Cell.id,
        moi.destination40Level.id
    )
    FROM MovementInstruction moi
    INNER JOIN moi.catStatus
    LEFT JOIN moi.eir eir
    WHERE moi.id = :movementInstructionId
""")
    MovementInstructionDetailsDto findMovementInstructionDetails(@Param("movementInstructionId") Integer movementInstructionId);

    @Modifying
    @Query("""
    UPDATE MovementInstruction mi
    SET mi.catStatus.id = :toAttendStatusId,
        mi.originBlock.id = :destiny20BlockId,
        mi.originCell.id = :destiny20CellId,
        mi.originLevel.id = :destiny20LevelId,
        mi.origin40Block.id = :destiny40BlockId,
        mi.origin40Cell.id = :destiny40CellId,
        mi.origin40Level.id = :destiny40LevelId
    WHERE mi.container.id = :containerId
      AND mi.catStatus.id = :toBeUpdatedStatusId
      AND mi.active = true
""")
    int updateMovementInstruction(
            @Param("toAttendStatusId") Integer toAttendStatusId,
            @Param("destiny20BlockId") Integer destiny20BlockId,
            @Param("destiny20CellId") Integer destiny20CellId,
            @Param("destiny20LevelId") Integer destiny20LevelId,
            @Param("destiny40BlockId") Integer destiny40BlockId,
            @Param("destiny40CellId") Integer destiny40CellId,
            @Param("destiny40LevelId") Integer destiny40LevelId,
            @Param("containerId") Integer containerId,
            @Param("toBeUpdatedStatusId") Integer toBeUpdatedStatusId
    );


    @Modifying
    @Query("""
    UPDATE MovementInstruction mi
    SET mi.catStatus.id = :cancelledStatusId
    WHERE mi.parentMovementInstruction.id = :parentInstructionId
      AND mi.catStatus.id IN (:holdedStatusId, :inProgressStatusId, :toAttendStatusId)
""")
    int cancelChildInstructions(
            @Param("cancelledStatusId") Integer cancelledStatusId,
            @Param("parentInstructionId") Integer parentInstructionId,
            @Param("holdedStatusId") Integer holdedStatusId,
            @Param("inProgressStatusId") Integer inProgressStatusId,
            @Param("toAttendStatusId") Integer toAttendStatusId
    );

    @Query("SELECT new com.maersk.sd1.sdy.dto.MovementInstructionConfirmOutput( " +
            " D20Clc.id,  " +
            " D40Clc.id,  " +
            " O20Blk.id,  " +
            " O20Blk.code,  " +
            " O20Cll.column,  " +
            " O20Cll.row,  " +
            " O20Lvl.index,  " +
            " O40Blk.code,  " +
            " O40Cll.column,  " +
            " O40Cll.row,  " +
            " O40Lvl.index,  " +
            " D20Blk.id,  " +
            " D20Blk.code,  " +
            " D20Cll.column,  " +
            " D20Cll.row,  " +
            " D20Lvl.index,  " +
            " D40Blk.code,  " +
            " D40Cll.column,  " +
            " D40Cll.row,  " +
            " D40Lvl.index,  " +
            " catSize.description,  " +
            " moi.catStatus.id,  " +
            " moi.catStatus.code,  " +
            " moi.catStatus.description,  " +
            " :respEstado,  " +
            " :respMensaje ) " +
            "FROM MovementInstruction moi " +
            "JOIN moi.container cnt " +
            "JOIN cnt.catSize catSize " +
            "LEFT JOIN moi.originBlock O20Blk " +
            "LEFT JOIN moi.originCell O20Cll " +
            "LEFT JOIN moi.originLevel O20Lvl " +
            "LEFT JOIN moi.origin40Block O40Blk " +
            "LEFT JOIN moi.origin40Cell O40Cll " +
            "LEFT JOIN moi.origin40Level O40Lvl " +
            "LEFT JOIN moi.destinationBlock D20Blk " +
            "LEFT JOIN moi.destinationCell D20Cll " +
            "LEFT JOIN moi.destinationLevel D20Lvl " +
            "LEFT JOIN moi.destination40Block D40Blk " +
            "LEFT JOIN moi.destination40Cell D40Cll " +
            "LEFT JOIN moi.destination40Level D40Lvl " +
            "LEFT JOIN com.maersk.sd1.common.model.ContainerLocation D20Clc ON (D20Clc.block.id = D20Blk.id AND D20Clc.cell.id = D20Cll.id AND D20Clc.level.id = D20Lvl.id) " +
            "LEFT JOIN com.maersk.sd1.common.model.ContainerLocation D40Clc ON (D40Clc.block.id = D40Blk.id AND D40Clc.cell.id = D40Cll.id AND D40Clc.level.id = D40Lvl.id) " +
            "WHERE moi.id = :movementInstructionId")
    MovementInstructionConfirmOutput getFinalResult(@Param("movementInstructionId") Integer movementInstructionId,
                                                    @Param("respEstado") String respEstado,
                                                    @Param("respMensaje") String respMensaje);

    @Modifying
    @Query("UPDATE MovementInstruction mi " +
            "SET mi.sequence = :sequence " +
            "WHERE mi.id = :id")
    void updateSequenceByMovementInstructionId(@Param("id") Integer id,
                                               @Param("sequence") Integer sequence);

    @Query("SELECT new com.maersk.sd1.sdy.dto.PlanificacionInstruccionMovimientoAtenderDto(" +
            " mi.id, " +
            " c.id, c.containerNumber, " +
            " catStatus.code, catStatus.description, " +
            " bloO.code, celO.column, celO.row, levO.index, " +
            " bloD.code, celD.column, celD.row, levD.index ) " +
            " FROM MovementInstruction mi " +
            " JOIN mi.container c " +
            " JOIN mi.catStatus catStatus " +
            " JOIN mi.originBlock bloO " +
            " JOIN mi.originCell celO " +
            " JOIN mi.originLevel levO " +
            " JOIN mi.destinationBlock bloD " +
            " JOIN mi.destinationCell celD " +
            " JOIN mi.destinationLevel levD " +
            " WHERE mi.id = :instructionId " +
            "   AND mi.active = true")
    Optional<PlanificacionInstruccionMovimientoAtenderDto> findMovementInstructionDataById(@Param("instructionId") Integer instructionId);

    Optional<MovementInstruction> findByIdAndActiveTrue(Integer id);


    @Query("select mi.container.containerNumber from MovementInstruction mi join mi.catStatus cs "
            + "where mi.destinationYard.id = :yardId "
            + "and mi.destinationBlock.id = :blockId "
            + "and mi.destinationCell.id = :cellId "
            + "and mi.destinationLevel.id = :levelId "
            + "and mi.container.id <> :containerId "
            + "and cs.code in ('CR','OH','IP','DP')")
    List<String> findAssignedMovementContainer(@Param("yardId") Integer yardId,
                                                @Param("blockId") Integer blockId,
                                                @Param("cellId") Integer cellId,
                                                @Param("levelId") Integer levelId,
                                                @Param("containerId") Integer containerId);

    @Query("SELECT mi.container.id FROM MovementInstruction mi " +
            "WHERE mi.active = TRUE " +
            "  AND mi.catStatus.id IN :pendingStatuses ")
    List<Integer> findContainersWithPendingInstructions(@Param("pendingStatuses") List<Integer> pendingStatuses);

    @Query("SELECT mi FROM MovementInstruction mi WHERE mi.container.id = :containerId AND mi.catStatus.id NOT IN (:executedId) AND mi.active = true")
    List<MovementInstruction> findMovementInstructionPending(@Param("containerId") Integer containerId,
                                                             @Param("executedId") List<Integer> status);

    @Query("""
    SELECT m.id
    FROM MovementInstruction m
    WHERE m.eir IS NOT NULL
      AND m.eir.id = :eirId
      AND m.yard.id = :yardId
      AND m.container.id = :containerId
      AND m.catStatus.id = :executedStateCatId
      AND m.catOperation.id = :gateInOperationTypeCatId
      AND m.active = true
    ORDER BY m.registrationDate DESC
""")
    List<Integer> findExecutedGateInInstructions(
            @Param("eirId") Integer eirId,
            @Param("containerId") Integer containerId,
            @Param("yardId") Integer yardId,
            @Param("executedStateCatId") Integer executedStateCatId,
            @Param("gateInOperationTypeCatId") Integer gateInOperationTypeCatId
    );

    @Query("SELECT m.id " +
            "FROM MovementInstruction m " +
            "WHERE m.container.id = :containerId " +
            "AND m.yard.id = :yardId " +
            "AND m.catOperation.id IN (42904, 42905) " +
            "AND m.active = true " +
            "AND m.catStatus.id IN (42895, 42897, 42898, 42899) " +
            "ORDER BY m.registrationDate DESC")
    List<Integer> findMovementInstructionInTruckCreated( @Param("containerId") Integer containerId,
                                                         @Param("yardId") Integer yardId);

    @Query("""
        SELECT
        con.id,
        con.containerNumber,
        cat.code,
        cat.description,
        b.code,
        c.column,
        c.row,
        l.index,
        b1.code,
        c1.column,
        c1.row,
        l1.index,
        b2.code,
        c2.column,
        c2.row,
        l2.index,
        b3.code,
        c3.column,
        c3.row,
        l3.index
        FROM MovementInstruction im
        LEFT JOIN Container con ON con.id = im.container.id
        LEFT JOIN Catalog cat ON cat.id = im.catStatus.id
        LEFT JOIN Block b ON b.id = im.originBlock.id
        LEFT JOIN Cell c ON c.id = im.originCell.id
        LEFT JOIN Level l ON l.id = im.originLevel.id
        LEFT JOIN Block b1 ON b1.id = im.destinationBlock.id
        LEFT JOIN Cell c1 ON c1.id = im.destinationCell.id
        LEFT JOIN Level l1 ON l1.id = im.destinationLevel.id
        LEFT JOIN Block b2 ON b2.id = im.origin40Block.id
        LEFT JOIN Cell c2 ON c2.id = im.origin40Cell.id
        LEFT JOIN Level l2 ON l2.id = im.origin40Level.id
        LEFT JOIN Block b3 ON b3.id = im.destination40Block.id
        LEFT JOIN Cell c3 ON c3.id = im.destination40Cell.id
        LEFT JOIN Level l3 ON l3.id = im.destination40Level.id
        WHERE im.id = :id AND im.active = true
    """)
    List<Object[]> findCompleteById(@Param("id") Integer id);

    @Transactional
    @Modifying
    @Query("UPDATE MovementInstruction im SET im.catStatus.id = :statusId WHERE im.id = :id")
    void updateStatusById(@Param("id") Integer id, @Param("statusId") Integer statusId);


    @Query(value = """
    SELECT
        i.instruccion_movimiento_id,
        i.contenedor_id,
        ud.ubicacion_contenedor_id,
        i.destino_patio_id,
        i.destino_bloque_id,
        i.destino_celda_id,
        i.destino_nivel_id,
        ud4.ubicacion_contenedor_id,
        i.destino40_patio_id,
        i.destino40_bloque_id,
        i.destino40_celda_id,
        i.destino40_nivel_id,
        uo.ubicacion_contenedor_id,
        i.origen_patio_id,
        i.origen_bloque_id,
        i.origen_celda_id,
        i.origen_nivel_id,
        uo4.ubicacion_contenedor_id,
        i.origen40_patio_id,
        i.orgein40_bloque_id,
        i.origen40_celda_id,
        i.origen40_nivel_id,
        i.estado_id,
        i.codigo_grupo_id,
        i.cola_trabajo_id,
        q.codigo,
        q.descripcion,
        q.por_defecto,
        q.activo,
        q.patio_id,
        i.fecha_registro,
        i.usuario_registro_id,
        e.numero_contenedor,
        e.cat_tamano_id,
        e.cat_familia_id,
        e.cat_tipo_contenedor_id,
        e.linea_naviera_id,
        e.tara,
        e.carga_maxima,
        e.codigo_iso_id,
        e.cat_clase_id,
        e.cat_tipo_reefer_id,
        e.cat_marca_motor_id,
        e.shipper_own,
        c.descripcion AS contenedor_tamanio,
        c.codigo,
        c.descripcion,
        uo.visit_id,
        ud.visit_id,
        uo4.visit_id,
        ud4.visit_id,
        p.ubicacion_contenedor_id,
        i.destino_propuesto_bloque_id,
        i.destino_celda_id,
        i.destino_nivel_id,
        i.contenedor_id,
        p.visit_id,
        ce.catalogo_padre_id,
        ce.descripcion,
        ce.descricion_larga,
        ce.codigo,
        ce.estado,
        i.cat_id,
        mov.catalogo_padre_id,
        mov.descripcion,
        mov.descricion_larga,
        mov.codigo,
        i.cat_operacion_id,
        op.catalogo_padre_id,
        op.descripcion,
        op.descricion_larga,
        op.codigo,
        i.cat_tipo_movimiento_id,
        tm.catalogo_padre_id,
        tm.descripcion,
        tm.descricion_larga,
        tm.codigo,
        co.fila,
        co.indice_fila,
        co.columna,
        co.indice_columna,
        co.bloqueado,
        nr.indice,
        cd.fila,
        cd.indice_fila,
        cd.columna,
        cd.indice_columna,
        cd.bloqueado,
        nd.indice,
        co40.fila,
        co40.indice_fila,
        co40.columna,
        co40.indice_columna,
        co40.bloqueado,
        nr40.indice,
        cd40.fila,
        cd40.indice_fila,
        cd40.columna,
        cd40.indice_columna,
        cd40.bloqueado,
        nd40.indice
    FROM sdy.instruccion_movimiento i
    INNER JOIN sdy.patio y ON y.patio_id = i.patio_id
    LEFT JOIN sds.contenedor e ON e.contenedor_id = i.contenedor_id
    LEFT JOIN ges.catalogo c ON c.catalogo_id = e.cat_tamano_id
    LEFT JOIN sdy.celda cd ON cd.celda_id = i.destino_celda_id
    LEFT JOIN sdy.celda co ON co.celda_id = i.origen_celda_id
    LEFT JOIN sdy.nivel nd ON nd.nivel_id = i.destino_nivel_id
    LEFT JOIN sdy.nivel nr ON nr.nivel_id = i.origen_nivel_id
    LEFT JOIN sdy.celda cd40 ON cd40.celda_id = i.destino40_celda_id
    LEFT JOIN sdy.celda co40 ON co40.celda_id = i.origen40_celda_id
    LEFT JOIN sdy.nivel nd40 ON nd40.nivel_id = i.destino40_nivel_id
    LEFT JOIN sdy.nivel nr40 ON nr40.nivel_id = i.origen40_nivel_id
    LEFT JOIN sdy.ubicacion_contenedor uo ON uo.bloque_id = i.origen_bloque_id AND uo.celda_id = i.origen_celda_id AND uo.nivel_id = i.origen_nivel_id
    LEFT JOIN sdy.ubicacion_contenedor uo4 ON uo4.bloque_id = i.orgein40_bloque_id AND uo4.celda_id = i.origen40_celda_id AND uo4.nivel_id = i.origen40_nivel_id
    LEFT JOIN sdy.ubicacion_contenedor ud ON ud.bloque_id = i.destino_bloque_id AND ud.celda_id = i.destino_celda_id AND ud.nivel_id = i.destino_nivel_id
    LEFT JOIN sdy.ubicacion_contenedor ud4 ON ud4.bloque_id = i.destino40_bloque_id AND ud4.celda_id = i.destino40_celda_id AND ud4.nivel_id = i.destino40_nivel_id
    LEFT JOIN sdy.ubicacion_contenedor p ON p.bloque_id = i.destino_propuesto_bloque_id AND p.celda_id = i.destino_propuesto_celda_id AND p.nivel_id = i.destino_propuesto_nivel_id
    LEFT JOIN ges.catalogo ce ON ce.catalogo_id = i.estado_id
    LEFT JOIN ges.catalogo mov ON mov.catalogo_id = i.cat_id
    LEFT JOIN ges.catalogo op ON op.catalogo_id = i.cat_operacion_id
    LEFT JOIN ges.catalogo tm ON tm.catalogo_id = i.cat_tipo_movimiento_id
    LEFT JOIN sdy.cola_trabajo q ON q.cola_trabajo_id = i.cola_trabajo_id
    WHERE i.activo = 1
      AND i.instruccion_movimiento_id = :movementInstructionId
""", nativeQuery = true)
    List<Object[]> RetrievePlanningInfo(@Param("movementInstructionId") Integer movementInstructionId);

    @Query(value = """
        SELECT
            mi.contenedor_id as container_id,
            mi.instruccion_movimiento_id as movement_instruction_id,
            mi.destino_bloque_id as destiny_block_id,
            mi.destino_celda_id as destiny_cell_id,
            mi.destino_nivel_id as destiny_level_id,
            b.codigo as destiny_block_code,
            CASE
                WHEN c40.celda_id IS NULL THEN c20.indice_fila
                WHEN c20.indice_fila < c40.indice_fila THEN c20.indice_fila
                ELSE c40.indice_fila
            END as destiny_row_index,
            c20.indice_columna as destiny_column_index,
            n.indice as destiny_level_index,
            v.placa as vehicle_plate,
            CASE
                WHEN c40.celda_id IS NULL THEN c20.fila
                WHEN c20.indice_fila < c40.indice_fila THEN c20.fila
                ELSE c40.fila
            END as destiny_row_label,
            c20.columna as destiny_column_label,
            mi.estado_id as movement_instruction_status_id
        FROM sdy.instruccion_movimiento mi
        INNER JOIN sdy.bloque b ON b.bloque_id = mi.destino_bloque_id
        INNER JOIN sdy.celda c20 ON c20.celda_id = mi.destino_celda_id
        INNER JOIN sdy.nivel n ON n.nivel_id = mi.destino_nivel_id
        LEFT OUTER JOIN sds.vehiculo v ON v.vehiculo_id = mi.camion_id
        LEFT OUTER JOIN sdy.celda c40 ON c40.celda_id = mi.destino40_celda_id
        WHERE mi.contenedor_id IN :containerIds
        AND mi.estado_id IN :statusIds
        """, nativeQuery = true)
    List<Map<String, Object>> findActiveMovementInstructions(
            @Param("containerIds") List<Integer> containerIds,
            @Param("statusIds") List<Integer> statusIds);


    @Query(value = """
        SELECT DISTINCT
            IM.instruccion_movimiento_id AS instruccionMovimientoId,
            C_ES.codigo AS instruccionMovimientoEstado,
            C_ES.descripcion AS instruccionMovimientoEstadoDesc,
            im.secuencia AS sequencia,
            UCO_R.cantidad_removidos AS rehToFech,
            CON.numero_contenedor AS numeroContenedor,
            C_CL.codigo AS grade,
            C_TY.descripcion AS typeContenedor,
            COI.codigo_iso AS contenedorIsocode,
            C_CL.codigo AS contenedorClase,
            GRC.codigo AS groupCode,
            LNV.nombre AS shippingLine,
            EMP.razon_social AS customer,
            R.Codigo AS recursoCodigo,
            CAM.placa AS camionPlaca,
            BLO_O.bloque_id AS origenBloqueId,
            CEL_O.celda_id AS origenCeldaId,
            BLO_O.codigo AS origenBloque,
            CEL_O.columna AS origenColumna,
            CEL_O.fila AS origenFila,
            NIV_O.indice AS origenNivel,
            BLO_D.codigo AS destinoBloque,
            CEL_D.columna AS destinoColumna,
            CEL_D.fila AS destinoFila,
            NIV_D.indice AS destinoNivel,
            IM.fecha_registro AS creationDate,
            IM.fecha_modificacion AS modifiedDate,
            C_ES.catalogo_id AS estadoId,
            CAT_OP.descripcion AS tipoMovimiento,
            CAT_OP.catalogo_id AS tipoMovimientoId,
            UCO_R.cantidad_removidos AS rehandlings,
            IM.requiere_camion AS requiereCamion
        FROM sdy.instruccion_movimiento IM
        INNER JOIN sds.contenedor AS CON ON CON.contenedor_id = IM.contenedor_id
        INNER JOIN ges.catalogo C_ES ON C_ES.catalogo_id = IM.estado_id
        INNER JOIN sdy.bloque AS BLO_O ON BLO_O.bloque_id = IM.origen_bloque_id
        INNER JOIN sdy.celda AS CEL_O ON CEL_O.celda_id = IM.origen_celda_id
        INNER JOIN sdy.nivel AS NIV_O ON NIV_O.nivel_id = IM.origen_nivel_id
        INNER JOIN sdy.bloque AS BLO_D ON BLO_D.bloque_id = IM.destino_bloque_id
        INNER JOIN sdy.celda AS CEL_D ON CEL_D.celda_id = IM.destino_celda_id
        INNER JOIN sdy.nivel AS NIV_D ON NIV_D.nivel_id = IM.destino_nivel_id
        INNER JOIN ges.catalogo CAT_OP ON CAT_OP.catalogo_id = IM.cat_operacion_id
        LEFT JOIN ges.catalogo C_CL ON C_CL.catalogo_id = CON.cat_clase_id
        LEFT JOIN sds.codigo_iso AS COI ON COI.codigo_iso_id = CON.codigo_iso_id
        LEFT JOIN sdy.recurso AS R ON R.recurso_id = IM.recurso_id
        LEFT JOIN sdy.codigo_grupo AS GRC ON GRC.codigo_grupo_id = IM.codigo_grupo_id
        LEFT JOIN sdy.camion AS CAM ON CAM.camion_id = IM.camion_id
        LEFT JOIN sds.linea_naviera AS LNV ON LNV.linea_naviera_id = CON.linea_naviera_id
        INNER JOIN sde.eir AS EIR ON EIR.contenedor_id = CON.contenedor_id
        LEFT JOIN ges.empresa AS EMP ON EMP.empresa_id = EIR.empresa_cliente_id
        LEFT JOIN ges.catalogo C_TY ON C_TY.catalogo_id = EIR.cat_empty_full_id
        OUTER APPLY (
            SELECT TOP(1) UBI_CON.cantidad_removidos
            FROM sdy.ubicacion_contenedor AS UBI_CON
            LEFT JOIN sdy.celda AS CEL ON CEL.celda_id = UBI_CON.celda_id
            WHERE UBI_CON.contenedor_id = IM.contenedor_id AND UBI_CON.activo = 1
            ORDER BY CEL.indice_fila
        ) AS UCO_R
        WHERE IM.cola_trabajo_id = :colaTrabajoId
          AND IM.activo = 1
          AND C_ES.codigo = 'EXE'
          AND IM.fecha_modificacion >= DATEADD(HOUR, (:horasHistorico * -1), GETDATE())
          AND (:subUnidadNegocioLocalId IS NULL OR EIR.sub_unidad_negocio_local_id = :subUnidadNegocioLocalId)
          AND (
              EIR.eir_id = (
                  SELECT TOP 1 LEIR.eir_id
                  FROM sde.eir LEIR
                  WHERE LEIR.contenedor_id = CON.contenedor_id
                  ORDER BY fecha_registro DESC
              )
          )
        ORDER BY IM.fecha_modificacion DESC
        """, nativeQuery = true)
    List<MovementInstructionProjection> findMovementInstructionsByColaTrabajoId(
            @Param("colaTrabajoId") Long colaTrabajoId,
            @Param("horasHistorico") Integer horasHistorico,
            @Param("subUnidadNegocioLocalId") Long subUnidadNegocioLocalId
    );

    @Query(value = """
        SELECT DISTINCT
            IM.instruccion_movimiento_id AS instruccionMovimientoId,
            C_ES.codigo AS instruccionMovimientoEstado,
            C_ES.descripcion AS instruccionMovimientoEstadoDesc,
            IM.secuencia AS sequencia,
            UCO_R.cantidad_removidos AS rehToFech,
            CON.numero_contenedor AS numeroContenedor,
            C_CL.codigo AS grade,
            C_TY.descripcion AS typeContenedor,
            COI.codigo_iso AS contenedorIsocode,
            C_CTG.codigo AS contenedorClase,
            GRC.codigo AS groupCode,
            LNV.nombre AS shippingLine,
            EMP.razon_social AS customer,
            R.Codigo AS recursoCodigo,
            CAM.placa AS camionPlaca,
            BLO_O.bloque_id AS origenBloqueId,
            CEL_O.celda_id AS origenCeldaId,
            BLO_O.codigo AS origenBloque,
            CEL_O.columna AS origenColumna,
            CEL_O.fila AS origenFila,
            NIV_O.indice AS origenNivel,
            BLO_D.codigo AS destinoBloque,
            CEL_D.columna AS destinoColumna,
            CEL_D.fila AS destinoFila,
            NIV_D.indice AS destinoNivel,
            IM.fecha_registro AS creationDate,
            IM.fecha_modificacion AS modifiedDate,
            C_ES.catalogo_id AS catalogoId,
            CAT_OP.descripcion AS tipoMovimiento,
            CAT_OP.catalogo_id AS tipoMovimientoId,
            UCO_R.cantidad_removidos AS rehandlings,
            IM.requiere_camion AS requiereCamion,
            CAT_SZE.codigo AS catTamanoDesc
        FROM sdy.instruccion_movimiento IM
        INNER JOIN sds.contenedor CON ON CON.contenedor_id = IM.contenedor_id
        INNER JOIN ges.catalogo CAT_SZE ON CAT_SZE.catalogo_id = CON.cat_tamano_id
        INNER JOIN ges.catalogo C_ES ON C_ES.catalogo_id = IM.estado_id
        INNER JOIN sdy.bloque BLO_O ON BLO_O.bloque_id = IM.origen_bloque_id
        INNER JOIN sdy.celda CEL_O ON CEL_O.celda_id = IM.origen_celda_id
        INNER JOIN sdy.nivel NIV_O ON NIV_O.nivel_id = IM.origen_nivel_id
        INNER JOIN sdy.bloque BLO_D ON BLO_D.bloque_id = IM.destino_bloque_id
        INNER JOIN sdy.celda CEL_D ON CEL_D.celda_id = IM.destino_celda_id
        INNER JOIN sdy.nivel NIV_D ON NIV_D.nivel_id = IM.destino_nivel_id
        INNER JOIN ges.catalogo CAT_OP ON CAT_OP.catalogo_id = IM.cat_operacion_id
        LEFT JOIN ges.catalogo C_CL ON C_CL.catalogo_id = CON.cat_clase_id
        LEFT JOIN ges.catalogo C_CTG ON C_CTG.catalogo_id = CON.cat_categoria_id
        LEFT JOIN sds.codigo_iso COI ON COI.codigo_iso_id = CON.codigo_iso_id
        LEFT JOIN sdy.recurso R ON R.recurso_id = IM.recurso_id
        LEFT JOIN sdy.codigo_grupo GRC ON GRC.codigo_grupo_id = IM.codigo_grupo_id
        LEFT JOIN sdy.camion CAM ON CAM.camion_id = IM.camion_id
        LEFT JOIN sds.linea_naviera LNV ON LNV.linea_naviera_id = CON.linea_naviera_id
        INNER JOIN sde.eir EIR ON EIR.contenedor_id = CON.contenedor_id
        LEFT JOIN ges.empresa EMP ON EMP.empresa_id = EIR.empresa_cliente_id
        LEFT JOIN ges.catalogo C_TY ON C_TY.catalogo_id = EIR.cat_empty_full_id
        OUTER APPLY (
            SELECT TOP(1) UBI_CON.cantidad_removidos
            FROM sdy.ubicacion_contenedor UBI_CON
            LEFT JOIN sdy.celda CEL ON CEL.celda_id = UBI_CON.celda_id
            WHERE UBI_CON.contenedor_id = IM.contenedor_id AND UBI_CON.activo = 1
            ORDER BY CEL.indice_fila
        ) UCO_R
        WHERE IM.cola_trabajo_id = :colaTrabajoId
          AND IM.activo = 1
          AND C_ES.codigo NOT IN ('CA','EXE')
          AND (:subUnidadNegocioLocalId IS NULL OR EIR.sub_unidad_negocio_local_id = :subUnidadNegocioLocalId)
          AND EIR.eir_id = (
              SELECT TOP 1 LEIR.eir_id
              FROM sde.eir LEIR
              WHERE LEIR.contenedor_id = CON.contenedor_id
              ORDER BY fecha_registro DESC
          )
        ORDER BY IM.secuencia
        """, nativeQuery = true)
    List<MovementInstructionProjection> findAllByColaTrabajoIdAndSubUnidadNegocio(
            @Param("colaTrabajoId") Long colaTrabajoId,
            @Param("subUnidadNegocioLocalId") Long subUnidadNegocioLocalId
    );

    @Modifying
    @Query("UPDATE MovementInstruction mi " +
           "SET mi.catStatus.id = :cancelledStatusId, " +
           "mi.modificationUser.id = :userId, " +
           "mi.modificationDate = CURRENT_TIMESTAMP, " +
           "mi.comment = 'CANCELLED_FROM_LOAD_CONTAINER' " +
           "WHERE mi.id IN :movementInstructionIds")
    int cancelPendingMovementInstructions(
        @Param("movementInstructionIds") List<Integer> movementInstructionIds,
        @Param("cancelledStatusId") Integer cancelledStatusId,
        @Param("userId") Integer userId);

    @Query("SELECT mi FROM MovementInstruction mi WHERE mi.container.id = :containerId AND mi.catStatus.id IN :statusIds ORDER BY mi.id DESC")
    List<MovementInstruction> findInProgressRecordsByContainer(@Param("containerId") Integer containerId, @Param("statusIds") List<Integer> statusIds);

    @Modifying
    @Query("UPDATE MovementInstruction mi " +
            " SET mi.catStatus.id = :cancelledStatus, " +
            "     mi.modificationUser.id = :userModificationId, " +
            "     mi.modificationDate = CURRENT_TIMESTAMP, " +
            "     mi.comment = 'EIR_DELETED' " +
            " WHERE mi.catStatus.id NOT IN :skipStatusList " +
            "   AND mi.catMovementType.id = :outMovementTypeId " +
            "   AND mi.eir.id = :eirId")
    int cancelPendingWorkOrders(@Param("eirId") Integer eirId,
                                @Param("outMovementTypeId") Integer outMovementTypeId,
                                @Param("skipStatusList") List<Integer> skipStatusList,
                                @Param("cancelledStatus") Integer cancelledStatus,
                                @Param("userModificationId") Integer userModificationId);

    @Query("SELECT new com.maersk.sd1.sdy.dto.MovementInstructionLocationDto(" +
            "i.originBlock.id, " +
            "i.originCell.id, " +
            "i.originLevel.id, " +
            "i.origin40Block.id, " +
            "i.origin40Cell.id, " +
            "i.origin40Level.id, " +
            "i.destinationBlock.id, " +
            "i.destinationCell.id, " +
            "i.destinationLevel.id, " +
            "i.destination40Block.id, " +
            "i.destination40Cell.id, " +
            "i.destination40Level.id, " +
            "i.container.id, " +
            "statusCat.code, " +
            "statusCat.description, " +
            "destBlockCat.code, " +
            "originBlockCat.code, " +
            "destBlock.code" +
            ") " +
            "FROM MovementInstruction i " +
            "JOIN Catalog statusCat ON statusCat.id = i.catStatus.id " +
            "JOIN i.originBlock originBlock " +
            "JOIN i.destinationBlock destBlock " +
            "LEFT JOIN Catalog originBlockCat ON originBlockCat.id = originBlock.catBlockType.id " +
            "LEFT JOIN Catalog destBlockCat ON destBlockCat.id = destBlock.catBlockType.id " +
            "WHERE i.id = :moveInstructionId")
    MovementInstructionLocationDto findLocationDtoByInstructionId(@Param("moveInstructionId") Integer moveInstructionId);

    @Query("SELECT new com.maersk.sd1.sdy.service.PendingPlanningInstructionService$PendingMovementInstructionDTO(" +
            "m.id, c.id, c.containerNumber) " +
            "FROM MovementInstruction m " +
            "JOIN m.container c " +
            "JOIN m.catOperation catOpt " +
            "WHERE m.catStatus.id IN :estadoIds " +
            "AND c.containerNumber LIKE CONCAT('%', :containerNumber, '%') " +
            "AND m.parentMovementInstruction IS NULL " +
            "AND m.yard.id = :yardId " +
            "AND catOpt.code <> 'REH' " +
            "AND m.active = true")
    List<PendingPlanningInstructionService.PendingMovementInstructionDTO> findPendingMovementInstructions(
            @Param("estadoIds") List<Integer> estadoIds,
            @Param("containerNumber") String containerNumber,
            @Param("yardId") Integer yardId
    );

    @Query("SELECT new com.maersk.sd1.sdy.service.PendingPlanningInstructionService$ContainerInfoDTO(" +
            "cnt.id, " +
            "cnt.containerNumber, " +
            "cnt.catSize.id, " +
            "catSize.code, " +
            "cnt.catFamily.id, " +
            "catFamily.description, " +
            "catGrade.description, " +
            "cnt.catContainerType.id, " +
            "catType.description, " +
            "cnt.catGrade.id, " +
            "COALESCE(smty.gateInEir.id, sfll.gateInEir.id), " +
            "CAST(NULL AS integer), " +
            "CAST(NULL AS string), " +
            "CAST(NULL AS string)) " +
            "FROM Container cnt " +
            "LEFT JOIN cnt.catSize catSize " +
            "LEFT JOIN cnt.catFamily catFamily " +
            "LEFT JOIN cnt.catGrade catGrade " +
            "LEFT JOIN cnt.catContainerType catType, " +
            "StockEmpty smty, " +
            "StockFull sfll " +
            "WHERE cnt.id IN :containerIds " +
            "AND (smty.container.id = cnt.id AND smty.inStock = true AND smty.active = true AND smty.subBusinessUnit.id = :subBusinessUnitId OR smty.id IS NULL) " +
            "AND (sfll.container.id = cnt.id AND sfll.inStock = true AND sfll.active = true AND sfll.subBusinessUnit.id = :subBusinessUnitId OR sfll.id IS NULL)")
    List<PendingPlanningInstructionService.ContainerInfoDTO> findContainerInfoByIds(
            @Param("containerIds") List<Integer> containerIds,
            @Param("subBusinessUnitId") Integer subBusinessUnitId
    );

    @Query(value = """
            SELECT
                MOI.contenedor_id AS container_id,
                MOI.instruccion_movimiento_id AS movement_instruction_id,
                WQR.cola_trabajo_recurso_id AS work_queue_resource_id,
                MOI.eir_id AS eir_id
            FROM sdy.instruccion_movimiento MOI (NOLOCK)
            INNER JOIN ges.catalogo CAT_OPT (NOLOCK) ON CAT_OPT.catalogo_id = MOI.cat_operacion_id
            LEFT OUTER JOIN sdy.cola_trabajo WQU (NOLOCK) ON WQU.cola_trabajo_id = MOI.cola_trabajo_id
            LEFT OUTER JOIN sdy.cola_trabajo_recurso WQR (NOLOCK) ON WQR.cola_trabajo_id = WQU.cola_trabajo_id
            LEFT OUTER JOIN sdy.recurso RSC (NOLOCK) ON RSC.recurso_id = WQR.recurso_id
            WHERE MOI.instruccion_movimiento_id IN :movementInstructionIds
              AND MOI.activo = 1
            """, nativeQuery = true)
    List<Object[]> findMovementInformationWithWorkQueueResources(
            @Param("movementInstructionIds") List<Integer> movementInstructionIds);

    @Query(value = """
            SELECT
                MOI.eir_reference_id AS eir_reference_id,
                MOI.eir_id AS reference_eir_id
            FROM (
                SELECT
                    MOI.eir_reference_id,
                    MOI.eir_id,
                    ROW_NUMBER() OVER (PARTITION BY MOI.eir_reference_id ORDER BY MOI.instruccion_movimiento_id DESC) AS rn
                FROM sdy.instruccion_movimiento MOI (NOLOCK)
                WHERE MOI.eir_reference_id IN :eirIds
                  AND MOI.activo = 1
            ) MOI
            WHERE MOI.rn = 1
            """, nativeQuery = true)
    List<Object[]> findLatestReferenceEirIds(
            @Param("eirIds") List<Integer> eirIds);

    @Query(value = """
            SELECT
                MOI.secuencia,
                MOI.instruccion_movimiento_id,
                CAT_OPT.variable_1,
                MOI.cola_trabajo_id,
                CAT_STT.descripcion,
                MOI.requiere_camion,

                IIF(TMI.reference_eir_id IS NULL, VHC.placa, RVHC.placa),
                IIF(TMI.reference_eir_id IS NULL, VHC_CMP.razon_social, RVHC_CMP.razon_social),
                RSC.unidad_negocio_id,
                RSC.codigo,
                RSC.nombre,
                IIF(TMI.reference_eir_id IS NULL, ISNULL(CHS.chassis_number,EIR.chassis_number), ISNULL(RCHS.chassis_number,REIR.chassis_number)),

                CNT.contenedor_id,
                CNT.numero_contenedor,
                CAT_SIZE.codigo,
                CAT_FAM.descripcion,
                CAT_GRD.descripcion,
                CAT_TYP.descripcion,
                CNT.cat_clase_id,
                CAT_CDT.descricion_larga,

                CAT_OBL.codigo,
                O20_BLK.codigo,
                O20_BLK.nombre,
                O20_CLL.fila,
                O20_CLL.columna,
                O20_LVL.indice,
                O40_BLK.codigo,
                O40_BLK.nombre,
                O40_CLL.fila,
                O40_CLL.columna,
                O40_LVL.indice,

                CAT_DBL.codigo,
                D20_BLK.codigo,
                D20_BLK.nombre,
                D20_CLL.fila,
                D20_CLL.columna,
                D20_LVL.indice,
                D40_BLK.codigo,
                D40_BLK.nombre,
                D40_CLL.fila,
                D40_CLL.columna,
                D40_LVL.indice,

                WQU.cola_trabajo_id,
                WQU.codigo,
                WQU.descripcion,
                TMI.work_queue_resource_id,

                YRD.patio_id,
                YRD.codigo,
                YRD.nombre,

                MOI.eir_id,
                CNT.cat_empty_full_id,
                CAT_CTG.descripcion,
                CNT.cat_tamano_id,
                CNT.cat_familia_id,
                CNT.cat_tipo_contenedor_id,
                ISNULL(DCG.documento_carga, ISNULL(DCG_GOF.documento_carga, BKG_GOE.numero_booking))

            FROM sdy.instruccion_movimiento MOI (NOLOCK)
            INNER JOIN ges.catalogo CAT_OPT (NOLOCK) ON CAT_OPT.catalogo_id = MOI.cat_operacion_id
            LEFT OUTER JOIN sdy.patio YRD (NOLOCK) ON YRD.patio_id = MOI.patio_id
            LEFT OUTER JOIN ges.catalogo CAT_STT (NOLOCK) ON CAT_STT.catalogo_id = MOI.estado_id

            -- Container data
            LEFT OUTER JOIN sds.contenedor CNT (NOLOCK) ON CNT.contenedor_id = MOI.contenedor_id
            LEFT OUTER JOIN ges.catalogo CAT_SIZE (NOLOCK) ON CAT_SIZE.catalogo_id = CNT.cat_tamano_id
            LEFT OUTER JOIN ges.catalogo CAT_FAM (NOLOCK) ON CAT_FAM.catalogo_id = CNT.cat_familia_id
            LEFT OUTER JOIN ges.catalogo CAT_GRD (NOLOCK) ON CAT_GRD.catalogo_id = CNT.cat_clase_id
            LEFT OUTER JOIN ges.catalogo CAT_TYP (NOLOCK) ON CAT_TYP.catalogo_id = CNT.cat_tipo_contenedor_id
            LEFT OUTER JOIN ges.catalogo CAT_CTG (NOLOCK) ON CAT_CTG.catalogo_id = CNT.cat_empty_full_id
            LEFT OUTER JOIN ges.catalogo CAT_CDT (NOLOCK) ON CAT_CDT.catalogo_id = sdg.fn_GetEquipmentConditionID(MOI.eir_id,:isContainer,'','GRAL')

            -- Reference EIR data
            LEFT OUTER JOIN (
                SELECT
                    MOI_INNER.contenedor_id,
                    MOI_INNER.instruccion_movimiento_id AS movement_instruction_id,
                    WQR_INNER.cola_trabajo_recurso_id AS work_queue_resource_id,
                    MOI_INNER.eir_id,
                    (
                        SELECT TOP 1 MI_REF.eir_id
                        FROM sdy.instruccion_movimiento MI_REF
                        WHERE MI_REF.eir_reference_id = MOI_INNER.eir_id
                          AND MI_REF.activo = 1
                        ORDER BY MI_REF.instruccion_movimiento_id DESC
                    ) AS reference_eir_id
                FROM sdy.instruccion_movimiento MOI_INNER (NOLOCK)
                LEFT OUTER JOIN sdy.cola_trabajo WQU_INNER (NOLOCK) ON WQU_INNER.cola_trabajo_id = MOI_INNER.cola_trabajo_id
                LEFT OUTER JOIN sdy.cola_trabajo_recurso WQR_INNER (NOLOCK) ON WQR_INNER.cola_trabajo_id = WQU_INNER.cola_trabajo_id
                WHERE MOI_INNER.instruccion_movimiento_id IN :movementInstructionIds
                  AND MOI_INNER.activo = 1
            ) TMI ON TMI.movement_instruction_id = MOI.instruccion_movimiento_id

            -- Reference EIR data
            LEFT OUTER JOIN sde.eir REIR (NOLOCK) ON REIR.eir_id = TMI.reference_eir_id
            LEFT OUTER JOIN sdh.eir_chassis RECH (NOLOCK) ON RECH.eir_chassis_id = REIR.eir_chassis_id
            LEFT OUTER JOIN sdh.chassis RCHS (NOLOCK) ON RCHS.chassis_id = RECH.chassis_id
            LEFT OUTER JOIN sds.vehiculo RVHC (NOLOCK) ON RVHC.vehiculo_id = REIR.vehiculo_id
            LEFT OUTER JOIN ges.empresa RVHC_CMP (NOLOCK) ON RVHC_CMP.empresa_id = REIR.empresa_transporte_id

            -- Current EIR data
            LEFT OUTER JOIN sde.eir EIR (NOLOCK) ON EIR.eir_id = MOI.eir_id
            LEFT OUTER JOIN sds.vehiculo VHC (NOLOCK) ON VHC.vehiculo_id = EIR.vehiculo_id
            LEFT OUTER JOIN ges.empresa VHC_CMP (NOLOCK) ON VHC_CMP.empresa_id = EIR.empresa_transporte_id
            LEFT OUTER JOIN sdh.eir_chassis ECH (NOLOCK) ON ECH.eir_chassis_id = EIR.eir_chassis_id
            LEFT OUTER JOIN sdh.chassis CHS (NOLOCK) ON CHS.chassis_id = ECH.chassis_id

            -- Work queue and resource data
            LEFT OUTER JOIN sdy.cola_trabajo_recurso WQR (NOLOCK) ON WQR.cola_trabajo_recurso_id = TMI.work_queue_resource_id
            LEFT OUTER JOIN sdy.cola_trabajo WQU (NOLOCK) ON WQU.cola_trabajo_id = WQR.cola_trabajo_id
            LEFT OUTER JOIN sdy.recurso RSC (NOLOCK) ON RSC.recurso_id = WQR.recurso_id

            -- Origin location data (20ft)
            INNER JOIN sdy.bloque O20_BLK (NOLOCK) ON O20_BLK.bloque_id = MOI.origen_bloque_id
            INNER JOIN sdy.celda O20_CLL (NOLOCK) ON O20_CLL.celda_id = MOI.origen_celda_id
            INNER JOIN sdy.nivel O20_LVL (NOLOCK) ON O20_LVL.nivel_id = MOI.origen_nivel_id
            LEFT OUTER JOIN sdy.bloque O40_BLK (NOLOCK) ON O40_BLK.bloque_id = MOI.orgein40_bloque_id
            LEFT OUTER JOIN sdy.celda O40_CLL (NOLOCK) ON O40_CLL.celda_id = MOI.origen40_celda_id
            LEFT OUTER JOIN sdy.nivel O40_LVL (NOLOCK) ON O40_LVL.nivel_id = MOI.origen40_nivel_id
            LEFT OUTER JOIN ges.catalogo CAT_OBL (NOLOCK) ON CAT_OBL.catalogo_id = O20_BLK.cat_bloque_id

            -- Destination location data (20ft)
            INNER JOIN sdy.bloque D20_BLK (NOLOCK) ON D20_BLK.bloque_id = MOI.destino_bloque_id
            INNER JOIN sdy.celda D20_CLL (NOLOCK) ON D20_CLL.celda_id = MOI.destino_celda_id
            INNER JOIN sdy.nivel D20_LVL (NOLOCK) ON D20_LVL.nivel_id = MOI.destino_nivel_id
            LEFT OUTER JOIN sdy.bloque D40_BLK (NOLOCK) ON D40_BLK.bloque_id = MOI.destino40_bloque_id
            LEFT OUTER JOIN sdy.celda D40_CLL (NOLOCK) ON D40_CLL.celda_id = MOI.destino40_celda_id
            LEFT OUTER JOIN sdy.nivel D40_LVL (NOLOCK) ON D40_LVL.nivel_id = MOI.destino40_nivel_id
            LEFT OUTER JOIN ges.catalogo CAT_DBL (NOLOCK) ON CAT_DBL.catalogo_id = D20_BLK.cat_bloque_id

            -- Document data
            LEFT OUTER JOIN sde.eir_documento_carga_detalle EDCD (NOLOCK) ON EDCD.eir_id = MOI.eir_id
            LEFT OUTER JOIN sds.documento_carga_detalle DCD (NOLOCK) ON DCD.documento_carga_detalle_id = EDCD.documento_carga_detalle_id
            LEFT OUTER JOIN sds.documento_carga DCG (NOLOCK) ON DCG.documento_carga_id = DCD.documento_carga_id
            LEFT OUTER JOIN sds.documento_carga DCG_GOF (NOLOCK) ON DCG_GOF.documento_carga_id = EIR.documento_carga_gof_id
            LEFT OUTER JOIN sds.booking BKG_GOE (NOLOCK) ON BKG_GOE.booking_id = EIR.booking_id_gout_light

            WHERE MOI.instruccion_movimiento_id IN :movementInstructionIds
              AND MOI.activo = 1
            """, nativeQuery = true)
    List<Object[]> findDetailedMovementInstructionData(
            @Param("movementInstructionIds") List<Integer> movementInstructionIds,
            @Param("isContainer") Integer isContainer);

}