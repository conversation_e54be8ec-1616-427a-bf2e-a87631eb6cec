package com.maersk.sd1.sdy.service;

import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sdy.dto.PendingPlanningInstructionInput;
import com.maersk.sd1.sdy.dto.PendingPlanningInstructionOutput;
import com.maersk.sd1.sdy.dto.UpdateLocationInputDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class PendingPlanningInstructionService {

    private static final Logger logger = LogManager.getLogger(PendingPlanningInstructionService.class);

    private final CatalogRepository catalogRepository;
    private final BusinessUnitRepository businessUnitRepository;
    private final YardRepository yardRepository;
    private final MovementInstructionRepository movementInstructionRepository;
    private final EirRepository eirRepository;
    private final ContainerRestrictionRepository containerRestrictionRepository;
    private final ContainerPreassignmentRepository containerPreassignmentRepository;

    @Transactional
    public List<PendingPlanningInstructionOutput> getPendingPlanningInstructions(PendingPlanningInstructionInput.Input input) {

        try {
            Catalog catOnHold = catalogRepository.findCatalogByParentAndCode("EMI", "OH").orElse(null);
            Catalog catInProgress = catalogRepository.findCatalogByParentAndCode("EMI", "IP").orElse(null);
            Catalog catToBeAttended = catalogRepository.findCatalogByParentAndCode("EMI", "DP").orElse(null);

            List<Integer> statusIds = List.of(
                    catOnHold!=null ? catOnHold.getId() : null,
                    catInProgress!=null ? catInProgress.getId() : null,
                    catToBeAttended!=null ? catToBeAttended.getId() : null
            );

            List<String> aliases = List.of("sd1_equipment_category_container", "43081", "43083");
            List<Object[]> results = catalogRepository.findIdsByAliases(aliases);
            Map<String, Integer> aliasToId = new HashMap<>();
            for (Object[] result : results) {
                aliasToId.put((String) result[0], (Integer) result[1]);
            }
            Map<String, Integer> catalogIds = new HashMap<>();
            catalogIds.put("isContainer", aliasToId.get("sd1_equipment_category_container"));
            catalogIds.put("isGateOut", aliasToId.get("43083"));
            catalogIds.put("isEmpty", aliasToId.get("43081"));
            logger.info("Catalog IDs: {}", catalogIds);


            Integer subBusinessUnitId = businessUnitRepository.findParentBusinessUnitIdByBusinessUnitId(input.getLocalBusinessUnitId());
            Integer yardId = yardRepository.findYardIdByBusinessUnitId(input.getLocalBusinessUnitId());

            List<PendingMovementInstructionDTO> pendingMovementInstructions = movementInstructionRepository.findPendingMovementInstructions(statusIds, input.getContainerNumber(), yardId);

            logger.info("Found {} pending movement instructions for container '{}' in yard {}",
                    pendingMovementInstructions.size(), input.getContainerNumber(), yardId);

            List<Integer> containerIds = pendingMovementInstructions.stream()
                    .map(PendingMovementInstructionDTO::getContainerId)
                    .toList();

            logger.info("Container IDs to process: {}", containerIds);

            List<ContainerInfoDTO> containerInfo = movementInstructionRepository.findContainerInfoByIds(containerIds, subBusinessUnitId);

            logger.info("Found {} container info records", containerInfo.size());

            Map<Integer, Integer> eirIdToCatEmptyFullId = eirRepository.findEmptyFullIdsByEirIds(
                    containerInfo.stream()
                            .map(ContainerInfoDTO::getLastGateInEirId)
                            .filter(Objects::nonNull)
                            .distinct()
                            .toList()
            ).stream().collect(Collectors.toMap(
                    obj -> (Integer) obj[0],
                    obj -> (Integer) obj[1]
            ));

            for (ContainerInfoDTO container : containerInfo) {
                Integer eirId = container.getLastGateInEirId();
                if (eirId != null && eirIdToCatEmptyFullId.containsKey(eirId)) {
                    container.setCatEmptyFullId(eirIdToCatEmptyFullId.get(eirId));
                }
            }

            logger.info("Updating container info with restriction reasons...");
            updateContainerInfoWithRestrictionReasons(containerInfo, subBusinessUnitId);

            logger.info("Updating container info with last preallocation booking numbers...");
            updateContainerInfoWithLastPreallocationBookingNumber(containerInfo, subBusinessUnitId, catalogIds);

            logger.info("Creating movements information...");
            List<MovementInformationDTO> movementsInformation = createMovementsInformation(pendingMovementInstructions);

            logger.info("Updating movements information with reference EIR IDs...");
            updateMovementsInformationWithReferenceEirIds(movementsInformation);

            logger.info("Building final result set with {} containers and {} movements...",
                    containerInfo.size(), movementsInformation.size());
            List<PendingPlanningInstructionOutput> output = buildFinalResultSet(
                    containerInfo, movementsInformation, catalogIds);

            logger.info("Successfully retrieved {} pending planning instructions", output.size());
            return output;

        }
        catch (Exception e) {
            logger.error("Error fetching pending planning instructions: ", e);
        }

        return List.of();
    }

    private void updateContainerInfoWithRestrictionReasons(List<ContainerInfoDTO> containerInfo, Integer subBusinessUnitId) {
        if (containerInfo == null || containerInfo.isEmpty()) {
            return;
        }

        List<ContainerInfoDTO> containersWithEmptyFullId = containerInfo.stream()
                .filter(container -> container.getCatEmptyFullId() != null)
                .toList();

        if (containersWithEmptyFullId.isEmpty()) {
            logger.debug("No containers with cat_empty_full_id found, skipping restriction reasons update");
            return;
        }

        Map<Integer, List<ContainerInfoDTO>> containersByEmptyFullId = containersWithEmptyFullId.stream()
                .collect(Collectors.groupingBy(ContainerInfoDTO::getCatEmptyFullId));

        for (Map.Entry<Integer, List<ContainerInfoDTO>> entry : containersByEmptyFullId.entrySet()) {
            Integer catEmptyFullId = entry.getKey();
            List<ContainerInfoDTO> containers = entry.getValue();

            List<Integer> containerIds = containers.stream()
                    .map(ContainerInfoDTO::getContainerId)
                    .toList();

            List<Object[]> restrictions = containerRestrictionRepository.findRestrictionsByContainerIdsSubBusinessUnitIdsAndCatEmptyFullIds(
                    Set.copyOf(containerIds), Set.of(subBusinessUnitId), Set.of(catEmptyFullId));

            Map<Integer, String> containerToRestrictions = restrictions.stream()
                    .collect(Collectors.toMap(
                            result -> (Integer) result[1],
                            result -> (String) result[3],
                            (existing, replacement) -> existing
                    ));

            for (ContainerInfoDTO container : containers) {
                String restrictionReasons = containerToRestrictions.get(container.getContainerId());
                if (restrictionReasons != null && !restrictionReasons.trim().isEmpty()) {
                    container.setRestrictionReasons(restrictionReasons.trim());
                    logger.debug("Updated container {} with restriction reasons: {}",
                            container.getContainerId(), restrictionReasons);
                }
            }
        }

        int updatedCount = containerInfo.stream()
                .mapToInt(c -> c.getRestrictionReasons() != null && !c.getRestrictionReasons().isEmpty() ? 1 : 0)
                .sum();
        logger.info("Updated {} containers with restriction reasons", updatedCount);
    }

    private void updateContainerInfoWithLastPreallocationBookingNumber(List<ContainerInfoDTO> containerInfo,
                                                                        Integer subBusinessUnitId,
                                                                        Map<String, Integer> catalogIds) {
        if (containerInfo == null || containerInfo.isEmpty()) {
            return;
        }

        List<Integer> containerIds = containerInfo.stream()
                .map(ContainerInfoDTO::getContainerId)
                .toList();

        if (containerIds.isEmpty()) {
            logger.debug("No container IDs found, skipping preallocation booking number update");
            return;
        }

        Integer isGateOut = catalogIds.get("isGateOut");
        Integer isEmpty = catalogIds.get("isEmpty");

        if (isGateOut == null || isEmpty == null) {
            logger.warn("Missing catalog IDs for gate out ({}) or empty ({}), skipping preallocation booking number update",
                    isGateOut, isEmpty);
            return;
        }

        List<Object[]> preallocationResults = containerPreassignmentRepository.findLatestPreallocationBookingNumbers(
                containerIds, subBusinessUnitId, isGateOut, isEmpty);

        Map<Integer, String> containerToBookingNumber = preallocationResults.stream()
                .collect(Collectors.toMap(
                        result -> (Integer) result[0],
                        result -> (String) result[1],
                        (existing, replacement) -> existing
                ));

        for (ContainerInfoDTO container : containerInfo) {
            String bookingNumber = containerToBookingNumber.get(container.getContainerId());
            if (bookingNumber != null && !bookingNumber.trim().isEmpty()) {
                container.setLastPreallocationBookingNumber(bookingNumber.trim());
                logger.debug("Updated container {} with last preallocation booking number: {}",
                        container.getContainerId(), bookingNumber);
            }
        }

        int updatedCount = containerInfo.stream()
                .mapToInt(c -> c.getLastPreallocationBookingNumber() != null && !c.getLastPreallocationBookingNumber().isEmpty() ? 1 : 0)
                .sum();
        logger.info("Updated {} containers with last preallocation booking numbers", updatedCount);
    }

    private List<MovementInformationDTO> createMovementsInformation(List<PendingMovementInstructionDTO> pendingMovementInstructions) {
        if (pendingMovementInstructions == null || pendingMovementInstructions.isEmpty()) {
            logger.debug("No pending movement instructions found, returning empty movements information");
            return List.of();
        }

        List<Integer> movementInstructionIds = pendingMovementInstructions.stream()
                .map(PendingMovementInstructionDTO::getMovementInstructionId)
                .toList();

        List<Object[]> movementInfoResults = movementInstructionRepository.findMovementInformationWithWorkQueueResources(movementInstructionIds);

        List<MovementInformationDTO> movementsInformation = movementInfoResults.stream()
                .map(result -> new MovementInformationDTO(
                        (Integer) result[0],
                        (Integer) result[1],
                        (Integer) result[2],
                        (Integer) result[3],
                        null
                ))
                .toList();

        logger.info("Created {} movement information records from {} pending instructions",
                movementsInformation.size(), pendingMovementInstructions.size());

        return movementsInformation;
    }

    private void updateMovementsInformationWithReferenceEirIds(List<MovementInformationDTO> movementsInformation) {
        if (movementsInformation == null || movementsInformation.isEmpty()) {
            logger.debug("No movements information found, skipping reference EIR ID update");
            return;
        }

        List<Integer> eirIds = movementsInformation.stream()
                .map(MovementInformationDTO::getEirId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();

        if (eirIds.isEmpty()) {
            logger.debug("No EIR IDs found in movements information, skipping reference EIR ID update");
            return;
        }

        List<Object[]> referenceEirResults = movementInstructionRepository.findLatestReferenceEirIds(eirIds);

        Map<Integer, Integer> eirToReferenceEirMap = referenceEirResults.stream()
                .collect(Collectors.toMap(
                        result -> (Integer) result[0],
                        result -> (Integer) result[1],
                        (existing, replacement) -> existing
                ));

        for (MovementInformationDTO movement : movementsInformation) {
            Integer eirId = movement.getEirId();
            if (eirId != null && eirToReferenceEirMap.containsKey(eirId)) {
                Integer referenceEirId = eirToReferenceEirMap.get(eirId);
                movement.setReferenceEirId(referenceEirId);
                logger.debug("Updated movement {} with reference EIR ID {} for EIR {}",
                        movement.getMovementInstructionId(), referenceEirId, eirId);
            }
        }

        int updatedCount =  movementsInformation.stream()
                .mapToInt(m -> m.getReferenceEirId() != null ? 1 : 0)
                .sum();
        logger.info("Updated {} movements with reference EIR IDs", updatedCount);
    }

    private List<PendingPlanningInstructionOutput> buildFinalResultSet(
            List<ContainerInfoDTO> containerInfo,
            List<MovementInformationDTO> movementsInformation,
            Map<String, Integer> catalogIds) {

        if (containerInfo == null || containerInfo.isEmpty() || movementsInformation == null || movementsInformation.isEmpty()) {
            logger.debug("No container info or movements information found, returning empty result");
            return List.of();
        }

        Map<Integer, ContainerInfoDTO> containerMap = containerInfo.stream()
                .collect(Collectors.toMap(ContainerInfoDTO::getContainerId, c -> c));

        Map<Integer, MovementInformationDTO> movementMap = movementsInformation.stream()
                .collect(Collectors.toMap(MovementInformationDTO::getMovementInstructionId, m -> m));

        List<Integer> movementInstructionIds = movementsInformation.stream()
                .map(MovementInformationDTO::getMovementInstructionId)
                .toList();

        List<Object[]> movementDetails = movementInstructionRepository.findDetailedMovementInstructionData(
                movementInstructionIds, catalogIds.get("isContainer"));

        List<PendingPlanningInstructionOutput> results = new ArrayList<>();

        for (Object[] detail : movementDetails) {
            try {
                PendingPlanningInstructionOutput output = mapMovementDetailToOutput(detail, containerMap, movementMap);
                if (output != null) {
                    results.add(output);
                }
            } catch (Exception e) {
                logger.error("Error mapping movement detail to output: ", e);
            }
        }


        results.sort((a, b) -> {
            Integer aQueueResourceId = a.getQueueResourceId();
            Integer bQueueResourceId = b.getQueueResourceId();

            if (aQueueResourceId == null && bQueueResourceId == null) {
                return Integer.compare(
                    a.getSequence() != null ? a.getSequence() : 0,
                    b.getSequence() != null ? b.getSequence() : 0
                );
            }
            if (aQueueResourceId == null) return 1;
            if (bQueueResourceId == null) return -1;

            int queueResourceComparison = aQueueResourceId.compareTo(bQueueResourceId);
            if (queueResourceComparison != 0) {
                return queueResourceComparison;
            }

            return Integer.compare(
                a.getSequence() != null ? a.getSequence() : 0,
                b.getSequence() != null ? b.getSequence() : 0
            );
        });

        logger.info("Built and sorted {} final result records from {} movement details", results.size(), movementDetails.size());
        return results;
    }

    private PendingPlanningInstructionOutput mapMovementDetailToOutput(
            Object[] detail,
            Map<Integer, ContainerInfoDTO> containerMap,
            Map<Integer, MovementInformationDTO> movementMap) {

        if (detail == null || detail.length < 20) {
            logger.warn("Invalid movement detail data, skipping");
            return null;
        }

        PendingPlanningInstructionOutput output = new PendingPlanningInstructionOutput();

        try {

            Integer sequence = (Integer) detail[0];
            Integer movementInstructionId = (Integer) detail[1];
            String operationType = (String) detail[2];
            Integer workQueueId = (Integer) detail[3];
            Boolean requiresTruck = (Boolean) detail[5];

            String vehiclePlate = (String) detail[6];
            String vehicleCompany = (String) detail[7];
            String chassisNumber = (String) detail[11];

            String resourceCode = (String) detail[9];
            String resourceName = (String) detail[10];

            Integer containerId = (Integer) detail[12];
            String containerNumber = (String) detail[13];
            String containerFamilyDescription = (String) detail[15];
            String containerGradeDescription = (String) detail[16];
            String containerTypeDescription = (String) detail[17];
            Integer containerGradeId = (Integer) detail[18];
            String containerCondition = (String) detail[19];

            ContainerInfoDTO container = containerMap.get(containerId);

            MovementInformationDTO movement = movementMap.get(movementInstructionId);

            output.setSequence(sequence);
            output.setMovementInstructionId(movementInstructionId);
            output.setContainerNumber(containerNumber);
            output.setMovementType(operationType);
            output.setWorkQueueId(workQueueId);
            output.setRequiresTruck(requiresTruck);
            output.setVehiclePlate(vehiclePlate);
            output.setVehicleCompany(vehicleCompany);
            output.setChassisNumber(chassisNumber);
            output.setQueueResourceAlias(resourceCode);
            output.setQueueResourceName(resourceName);
            output.setContainerCondition(containerCondition);

            output.setFamilyDescription(containerFamilyDescription);
            output.setClassDescription(containerGradeDescription);
            output.setContainerTypeDescription(containerTypeDescription);
            output.setClassCategoryId(containerGradeId);

            if (container != null) {
                output.setContainerSize(container.getContainerSizeId());
                output.setSizeCategoryId(container.getContainerSizeId());
                output.setFamilyCategoryId(container.getContainerFamilyId());
                output.setTypeCategoryId(container.getContainerTypeId());
                output.setEmptyFullId(container.getCatEmptyFullId());
                output.setRestrictionReasons(container.getRestrictionReasons());
                output.setLastPreallocationBookingNumber(container.getLastPreallocationBookingNumber());
            }

            if (movement != null) {
                output.setEirId(movement.getEirId());
                output.setQueueResourceId(movement.getWorkQueueResourceId());
            }

            extractLocationData(detail, output);

            extractWorkQueueData(detail, output);

            extractYardData(detail, output);

            extractDocumentData(detail, output);

            extractAdditionalFields(detail, output);

            return output;

        } catch (Exception e) {
            logger.error("Error mapping movement detail to output: ", e);
            return null;
        }
    }

    private void extractLocationData(Object[] detail, PendingPlanningInstructionOutput output) {
        try {
            String originBlockTypeCode = (String) detail[20];
            String originBlockCode = (String) detail[21];
            String originRow = (String) detail[23];
            String originColumn = (String) detail[24];
            Integer originLevel = (Integer) detail[25];

            String origin40BlockCode = (String) detail[26];
            String origin40Row = (String) detail[28];
            String origin40Column = (String) detail[29];
            Integer origin40Level = (Integer) detail[30];

            String destBlockTypeCode = (String) detail[31];
            String destBlockCode = (String) detail[32];
            String destRow = (String) detail[34];
            String destColumn = (String) detail[35];
            Integer destLevel = (Integer) detail[36];

            String dest40BlockCode = (String) detail[37];
            String dest40Row = (String) detail[39];
            String dest40Column = (String) detail[40];
            Integer dest40Level = (Integer) detail[41];

            UpdateLocationInputDTO.ContainerLocationValue initialLocation = new UpdateLocationInputDTO.ContainerLocationValue();
            initialLocation.setBlockCode(originBlockCode != null ? originBlockCode : origin40BlockCode);
            initialLocation.setRow(originRow != null ? originRow : origin40Row);
            initialLocation.setColumn(originColumn != null ? originColumn : origin40Column);
            initialLocation.setLevel(originLevel != null ? originLevel : origin40Level);
            initialLocation.setBlockType(originBlockTypeCode);
            initialLocation.setBlockCode40(origin40BlockCode);
            initialLocation.setRow40(origin40Row);
            initialLocation.setColumn40(origin40Column);
            initialLocation.setLevel40(origin40Level);

            UpdateLocationInputDTO.ContainerLocationValue finalLocation = new UpdateLocationInputDTO.ContainerLocationValue();
            finalLocation.setBlockCode(destBlockCode != null ? destBlockCode : dest40BlockCode);
            finalLocation.setRow(destRow != null ? destRow : dest40Row);
            finalLocation.setColumn(destColumn != null ? destColumn : dest40Column);
            finalLocation.setLevel(destLevel != null ? destLevel : dest40Level);
            finalLocation.setBlockType(destBlockTypeCode);
            finalLocation.setBlockCode40(dest40BlockCode);
            finalLocation.setRow40(dest40Row);
            finalLocation.setColumn40(dest40Column);
            finalLocation.setLevel40(dest40Level);

            output.setInitialLocation(initialLocation);
            output.setFinalLocation(finalLocation);

        } catch (Exception e) {
            logger.error("Error extracting location data: ", e);
        }
    }

    private void extractYardData(Object[] detail, PendingPlanningInstructionOutput output) {
        try {
            String yardCode = (String) detail[46];
            String yardName = (String) detail[47];

            output.setYardCode(yardCode);
            output.setYardName(yardName);

        } catch (Exception e) {
            logger.error("Error extracting yard data: ", e);
        }
    }

    private void extractDocumentData(Object[] detail, PendingPlanningInstructionOutput output) {
        try {
            String documentNumber = (String) detail[54];

            output.setDocumentNumber(documentNumber);

        } catch (Exception e) {
            logger.error("Error extracting document data: ", e);
        }
    }

    private void extractWorkQueueData(Object[] detail, PendingPlanningInstructionOutput output) {
        try {
            String queueCode = (String) detail[43];
            String queueDescription = (String) detail[44];

            output.setQueueCode(queueCode);
            output.setQueueDescription(queueDescription);

        } catch (Exception e) {
            logger.error("Error extracting work queue data: ", e);
        }
    }

    private void extractAdditionalFields(Object[] detail, PendingPlanningInstructionOutput output) {
        try {

            Integer eirId = (Integer) detail[48];
            Integer emptyFullId = (Integer) detail[49];
            String emptyFullDescription = (String) detail[50];
            Integer sizeCategoryId = (Integer) detail[51];
            Integer familyCategoryId = (Integer) detail[52];
            Integer typeCategoryId = (Integer) detail[53];

            if (output.getEirId() == null) {
                output.setEirId(eirId);
            }
            if (output.getEmptyFullId() == null) {
                output.setEmptyFullId(emptyFullId);
            }
            if (output.getEmptyFullDescription() == null) {
                output.setEmptyFullDescription(emptyFullDescription);
            }
            if (output.getSizeCategoryId() == null) {
                output.setSizeCategoryId(sizeCategoryId);
            }
            if (output.getFamilyCategoryId() == null) {
                output.setFamilyCategoryId(familyCategoryId);
            }
            if (output.getTypeCategoryId() == null) {
                output.setTypeCategoryId(typeCategoryId);
            }

        } catch (Exception e) {
            logger.error("Error extracting additional fields: ", e);
        }
    }

    @Data
    @AllArgsConstructor
    public static class PendingMovementInstructionDTO {
        private Integer movementInstructionId;
        private Integer containerId;
        private String containerNumber;
    }

    @Data
    @AllArgsConstructor
    public static class ContainerInfoDTO {
        private Integer containerId;
        private String containerNumber;
        private Integer containerSizeId;
        private String containerSizeCode;
        private Integer containerFamilyId;
        private String containerFamilyDescription;
        private String containerGradeDescription;
        private Integer containerTypeId;
        private String containerTypeDescription;
        private Integer containerGradeId;
        private Integer lastGateInEirId;
        private Integer catEmptyFullId;
        private String restrictionReasons;
        private String lastPreallocationBookingNumber;
    }

    @Data
    @AllArgsConstructor
    public static class MovementInformationDTO {
        private Integer containerId;
        private Integer movementInstructionId;
        private Integer workQueueResourceId;
        private Integer eirId;
        private Integer referenceEirId;
    }

}
