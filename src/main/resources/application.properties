#OLD Configs
spring.jackson.date-format=yyyy-MM-dd'T'HH:mm:ss
server.servlet.contextPath=/
#New Configs
server.port=8095
spring.datasource.driverClassName=com.microsoft.sqlserver.jdbc.SQLServerDriver
#spring.datasource.url=************************************************************************************
#spring.datasource.username=sd1admin
#spring.datasource.password=mUPSLab$$234
spring.datasource.url=*********************************************************************************************
spring.datasource.username=adminapm
spring.datasource.password=Pr0t0c0l0
#spring.datasource.url=********************************************************************************************
#spring.datasource.username=adminsqlqa
#spring.datasource.password=H${:3Q]pE7X&N7JW
spring.jpa.show-sql=true

#Properties file
jpo.ds.type=SQLSERVER
jpo.ds.url=mssql-server-sd1-dev.database.windows.net
jpo.ds.db=mssql-db-sd1-dev
jpo.ds.scheme=
jpo.ds.username=sd1admin
jpo.ds.password=mUPSLab$$234
jpo.dsoauth2.type=SQLSERVER
jpo.dsoauth2.url=mssql-server-sd1-dev.database.windows.net
jpo.dsoauth2.db=mssql-db-sd1-dev
jpo.dsoauth2.scheme=
jpo.dsoauth2.username=sd1admin
jpo.dsoauth2.password=mUPSLab$$234
jpo.dsinland.type=SQLSERVER
jpo.dsinland.url=mssql-server-sd1-dev.database.windows.net
jpo.dsinland.db=mssql-db-sd1-dev
jpo.dsinland.scheme=
jpo.dsinland.username=sd1admin
jpo.dsinland.password=mUPSLab$$234
# Properties sendgrid
jpo.mail.attachment.img=C:\\SHARED\\LogoAPM_CO.png
jpo.mail.attachment.name=LogoAPM_CO.png
jpo.mail.attachment.id=LogoAPM_CO
jpo.mail.sendgrid.api.key=*********************************************************************
# Needed for SDY update to Spring 2.6.4
spring.mvc.pathmatch.matching-strategy = ANT_PATH_MATCHER
################### Crons     ##########################
#0 * * ? * *	=> Every 1 minutes
#0 */2 * ? * *	=> Every 1 minutes
#0 */15 * ? * *	=> Every 15 minutes
#0 */30 * ? * *	=> Every 30 minutes
#0 0 * ? * *	=> Every hour
#0 0 */3 ? * *	=> Every three hours
cron.taskChassisEdi=0 * * ? * *
#
#


msk.sdg.api.url.appointmentSearchEquipment=https://appointmentprep.inlandservices.com/api/moduleaps/ModuleAPS/module/aps/APSAppointmentServiceImp/apsappointmentSearchByEquipment
msk.sdg.api.url.appointmentSearchDocument=https://appointmentprep.inlandservices.com/api/moduleaps/ModuleAPS/module/aps/APSAppointmentServiceImp/apsappointmentSearchByDocument
msk.sdg.api.url.attendAppointment=https://appointmentprep.inlandservices.com/api/moduleaps/ModuleAPS/module/aps/APSAppointmentServiceImp/apscitaAtender
msk.api.apiUserLogin.aps=http://appointmentprep.inlandservices.com/api/inlandnet/Inlandnet/security/UserServiceImp/apiUserLoginv2
msk.api.apiUserLogin.aps.user=<EMAIL>
msk.api.apiUserLogin.aps.password=/B6u7i2pKxSeXf/M0UoFXnjdv+OJ4XjEkgG9ExicuP4=
msk.api.apiUserLogin.aps.system=APS
msk.sdg.api.url.comodatoSearchByEquipment=https://tucontenedor360.inlandservices.com/ModuleTCO/Business?method=tcoblsContenedorObtener&package=module.tco&class=TCOConsultaServiceImp
msk.api.apiUserLogin.sdy.loginUrl=https://inlandnet.dev.maersk-digital.net/Inlandnet/security/UserServiceImp/apiUserLogin
msk.api.apiUserLogin.sdy.user=<EMAIL>
msk.api.apiUserLogin.sdy.password=WhWR8BKZCxI2YWOF0rA5YV2xUv+O7gJv5yt8nJ4RlSU=
msk.api.apiUserLogin.sdy.system=9
msk.api.apiUserLogin.sdy.gateInUrl=https://modulesdy.dev.maersk-digital.net/ModuleSDY/moduleYard/sdy/SDYPlanificacionServiceImp/sdyPlannigGateInAssignment
msk.api.apiUserLogin.sdy.gateOutUrl=https://modulesdy.dev.maersk-digital.net/ModuleSDY/moduleYard/sdy/SDYPlanificacionServiceImp/sdyPlanningGateOut
msk.api.apiUserLogin.sdy.searchContainer=https://modulesdy.dev.maersk-digital.net/ModuleSDY/moduleYard/sdy/SDYPlanificacionServiceImp/sdybuscarContenedoresParaSalida
msk.api.apiUserLogin.sdy.plannigGateInAssignmentAndAprrove=https://modulesdy.dev.maersk-digital.net/ModuleSDY/moduleYard/sdy/SDYPlanificacionServiceImp/sdyPlannigGateInAssignmentAndAprrove
msk.api.apiUserLogin.sdy.afterTruckDepartureCreateWorkOrder=https://modulesdy.dev.maersk-digital.net/ModuleSDY/moduleYard/sdy/SDYPlanificacionServiceImp/sdyAfterTruckDepartureCreateWorkOrder
msk.cat.type_movement.gateout=43081
testcon.api.url=http://localhost:8080/ServiceTestConnection/

# Expose health endpoint
#added for livenessState & readinessState
management.endpoints.web.exposure.include=health
management.health.livenessState.enabled=true
management.health.readinessState.enabled=true

#SDS Properties
#Properties file
apsint.api.alias=APS
apsint.api.login.user=<EMAIL>
apsint.api.login.password=/B6u7i2pKxSeXf/M0UoFXnjdv+OJ4XjEkgG9ExicuP4=
apsint.api.login.url=https://appointment.inlandservices.com/api/inlandnet/Inlandnet/security/UserServiceImp/apiUserLogin
apsint.api.getAppointment.url=https://appointment.inlandservices.com/api/moduleaps/ModuleAPS/module/aps/APSAppointmentRestServiceImp/apsappointmentGet
apsint.api.attendAppointment.url=https://appointment.inlandservices.com/api/moduleaps/ModuleAPS/module/aps/APSAppointmentServiceImp/apscitaAtender
apsint.api.getAppointmentByContainer.url=https://appointment.inlandservices.com/api/moduleaps/ModuleAPS/module/aps/APSAppointmentServiceImp/apscitaObtenerByContenedor
apsint.api.getAppointmentByDocument.url=https://appointment.inlandservices.com/api/moduleaps/ModuleAPS/module/aps/APSAppointmentServiceImp/apsgetAppointmentsByDocument
apsint.api.updateAppointmentStatus.url=https://appointment.inlandservices.com/api/moduleaps/ModuleAPS/module/aps/APSAppointmentServiceImp/apsupdateAppointmentStatus