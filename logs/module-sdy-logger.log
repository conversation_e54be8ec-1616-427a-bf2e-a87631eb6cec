2025-06-12 10:56:34,838 INFO o.s.b.StartupInfoLogger [main] Starting BusinessApplication using Java 23.0.1 with PID 12792 (C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes started by AKA665 in C:\Users\<USER>\Documents\SD1 SDY)
2025-06-12 10:56:34,855 INFO o.s.b.SpringApplication [main] No active profile set, falling back to 1 default profile: "default"
2025-06-12 10:56:52,961 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-12 10:56:55,499 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Finished Spring Data repository scanning in 2507 ms. Found 297 JPA repository interfaces.
2025-06-12 10:56:58,256 INFO o.s.b.w.e.t.TomcatWebServer [main] Tomcat initialized with port 8095 (http)
2025-06-12 10:56:58,272 INFO o.a.j.l.DirectJDKLog [main] Initializing ProtocolHandler ["http-nio-8095"]
2025-06-12 10:56:58,289 INFO o.a.j.l.DirectJDKLog [main] Starting service [Tomcat]
2025-06-12 10:56:58,289 INFO o.a.j.l.DirectJDKLog [main] Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-12 10:56:58,428 INFO o.a.j.l.DirectJDKLog [main] Initializing Spring embedded WebApplicationContext
2025-06-12 10:56:58,428 INFO o.s.b.w.s.c.ServletWebServerApplicationContext [main] Root WebApplicationContext: initialization completed in 23407 ms
2025-06-12 10:57:00,361 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Starting...
2025-06-12 10:57:04,094 INFO c.z.h.p.HikariPool [main] HikariPool-1 - Added connection ConnectionID:1 ClientConnectionId: db9b7b45-8dc1-4f34-9a02-f159b4267240
2025-06-12 10:57:04,095 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Start completed.
2025-06-12 10:57:04,177 INFO o.h.j.i.u.LogHelper [main] HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-12 10:57:04,286 INFO o.h.Version [main] HHH000412: Hibernate ORM core version 6.3.1.Final
2025-06-12 10:57:04,364 INFO o.h.c.i.RegionFactoryInitiator [main] HHH000026: Second-level cache disabled
2025-06-12 10:57:04,810 INFO o.s.o.j.p.SpringPersistenceUnitInfo [main] No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-12 10:57:07,850 WARN o.h.m.RootClass [main] HHH000038: Composite-id class does not override equals(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-12 10:57:07,850 WARN o.h.m.RootClass [main] HHH000039: Composite-id class does not override hashCode(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-12 10:57:11,377 INFO o.h.e.t.j.p.i.JtaPlatformInitiator [main] HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-12 10:57:11,422 INFO o.s.o.j.AbstractEntityManagerFactoryBean [main] Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-12 10:57:12,435 INFO o.s.d.j.r.q.QueryEnhancerFactory [main] Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-12 10:57:16,207 WARN o.s.c.s.AbstractApplicationContext [main] Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'planificacionCambioZonaUseCaseImpl' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\business\core\planing\usecase\planificacion\PlanificacionCambioZonaUseCaseImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'planingConfig': Unsatisfied dependency expressed through field 'findMostAvailableFullContainerGateOutService': Error creating bean with name 'findMostAvailableFullContainerGateOutService' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\sdy\service\FindMostAvailableFullContainerGateOutService.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'movementInstructionRepository' defined in com.maersk.sd1.common.repository.MovementInstructionRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findPendingMovementInstructions(java.util.List,java.lang.String,java.lang.Integer); Reason: Validation failed for query for method public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findPendingMovementInstructions(java.util.List,java.lang.String,java.lang.Integer)
2025-06-12 10:57:16,207 INFO o.s.o.j.AbstractEntityManagerFactoryBean [main] Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-12 10:57:16,211 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Shutdown initiated...
2025-06-12 10:57:17,288 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Shutdown completed.
2025-06-12 10:57:18,306 WARN z.r.i.AsyncReporter$BoundedAsyncReporter [main] Timed out waiting for in-flight spans to send
2025-06-12 10:57:18,313 INFO o.a.j.l.DirectJDKLog [main] Stopping service [Tomcat]
2025-06-12 10:57:18,332 INFO o.s.b.a.l.ConditionEvaluationReportLogger [main] 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-06-12 10:57:18,356 ERROR o.s.b.SpringApplication [main] Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'planificacionCambioZonaUseCaseImpl' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\business\core\planing\usecase\planificacion\PlanificacionCambioZonaUseCaseImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'planingConfig': Unsatisfied dependency expressed through field 'findMostAvailableFullContainerGateOutService': Error creating bean with name 'findMostAvailableFullContainerGateOutService' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\sdy\service\FindMostAvailableFullContainerGateOutService.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'movementInstructionRepository' defined in com.maersk.sd1.common.repository.MovementInstructionRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findPendingMovementInstructions(java.util.List,java.lang.String,java.lang.Integer); Reason: Validation failed for query for method public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findPendingMovementInstructions(java.util.List,java.lang.String,java.lang.Integer)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:795)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1212)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.maersk.sd1.BusinessApplication.main(BusinessApplication.java:13)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'planingConfig': Unsatisfied dependency expressed through field 'findMostAvailableFullContainerGateOutService': Error creating bean with name 'findMostAvailableFullContainerGateOutService' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\sdy\service\FindMostAvailableFullContainerGateOutService.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'movementInstructionRepository' defined in com.maersk.sd1.common.repository.MovementInstructionRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findPendingMovementInstructions(java.util.List,java.lang.String,java.lang.Integer); Reason: Validation failed for query for method public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findPendingMovementInstructions(java.util.List,java.lang.String,java.lang.Integer)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:409)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1355)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1185)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:782)
	... 19 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'findMostAvailableFullContainerGateOutService' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\sdy\service\FindMostAvailableFullContainerGateOutService.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'movementInstructionRepository' defined in com.maersk.sd1.common.repository.MovementInstructionRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findPendingMovementInstructions(java.util.List,java.lang.String,java.lang.Integer); Reason: Validation failed for query for method public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findPendingMovementInstructions(java.util.List,java.lang.String,java.lang.Integer)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:795)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1212)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 43 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'movementInstructionRepository' defined in com.maersk.sd1.common.repository.MovementInstructionRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findPendingMovementInstructions(java.util.List,java.lang.String,java.lang.Integer); Reason: Validation failed for query for method public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findPendingMovementInstructions(java.util.List,java.lang.String,java.lang.Integer)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1806)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:782)
	... 56 common frames omitted
Caused by: org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findPendingMovementInstructions(java.util.List,java.lang.String,java.lang.Integer); Reason: Validation failed for query for method public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findPendingMovementInstructions(java.util.List,java.lang.String,java.lang.Integer)
	at org.springframework.data.repository.query.QueryCreationException.create(QueryCreationException.java:101)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:119)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.mapMethodsToQuery(QueryExecutorMethodInterceptor.java:103)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lambda$new$0(QueryExecutorMethodInterceptor.java:92)
	at java.base/java.util.Optional.map(Optional.java:260)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.<init>(QueryExecutorMethodInterceptor.java:92)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport.getRepository(RepositoryFactorySupport.java:357)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.lambda$afterPropertiesSet$5(RepositoryFactoryBeanSupport.java:290)
	at org.springframework.data.util.Lazy.getNullable(Lazy.java:135)
	at org.springframework.data.util.Lazy.get(Lazy.java:113)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.afterPropertiesSet(RepositoryFactoryBeanSupport.java:296)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean.afterPropertiesSet(JpaRepositoryFactoryBean.java:132)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1802)
	... 67 common frames omitted
Caused by: java.lang.IllegalArgumentException: Validation failed for query for method public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findPendingMovementInstructions(java.util.List,java.lang.String,java.lang.Integer)
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.validateQuery(SimpleJpaQuery.java:100)
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.<init>(SimpleJpaQuery.java:70)
	at org.springframework.data.jpa.repository.query.JpaQueryFactory.fromMethodWithQueryString(JpaQueryFactory.java:60)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$DeclaredQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:170)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateIfNotFoundQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:252)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$AbstractQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:95)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:115)
	... 79 common frames omitted
Caused by: java.lang.IllegalArgumentException: org.hibernate.query.sqm.UnknownEntityException: Could not resolve root entity 'InstruccionMovimiento'
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:143)
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:167)
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:173)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:802)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:707)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:132)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.orm.jpa.ExtendedEntityManagerCreator$ExtendedEntityManagerInvocationHandler.invoke(ExtendedEntityManagerCreator.java:364)
	at jdk.proxy2/jdk.proxy2.$Proxy172.createQuery(Unknown Source)
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.validateQuery(SimpleJpaQuery.java:94)
	... 85 common frames omitted
Caused by: org.hibernate.query.sqm.UnknownEntityException: Could not resolve root entity 'InstruccionMovimiento'
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.resolveRootEntity(SemanticQueryBuilder.java:2012)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitRootEntity(SemanticQueryBuilder.java:1943)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitRootEntity(SemanticQueryBuilder.java:268)
	at org.hibernate.grammars.hql.HqlParser$RootEntityContext.accept(HqlParser.java:2515)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitEntityWithJoins(SemanticQueryBuilder.java:1913)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitFromClause(SemanticQueryBuilder.java:1900)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitQuery(SemanticQueryBuilder.java:1147)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitQuerySpecExpression(SemanticQueryBuilder.java:940)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitQuerySpecExpression(SemanticQueryBuilder.java:268)
	at org.hibernate.grammars.hql.HqlParser$QuerySpecExpressionContext.accept(HqlParser.java:1844)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitSimpleQueryGroup(SemanticQueryBuilder.java:925)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitSimpleQueryGroup(SemanticQueryBuilder.java:268)
	at org.hibernate.grammars.hql.HqlParser$SimpleQueryGroupContext.accept(HqlParser.java:1718)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitSelectStatement(SemanticQueryBuilder.java:442)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitStatement(SemanticQueryBuilder.java:401)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.buildSemanticModel(SemanticQueryBuilder.java:310)
	at org.hibernate.query.hql.internal.StandardHqlTranslator.translate(StandardHqlTranslator.java:71)
	at org.hibernate.query.internal.QueryInterpretationCacheStandardImpl.createHqlInterpretation(QueryInterpretationCacheStandardImpl.java:165)
	at org.hibernate.query.internal.QueryInterpretationCacheStandardImpl.resolveHqlInterpretation(QueryInterpretationCacheStandardImpl.java:147)
	at org.hibernate.internal.AbstractSharedSessionContract.interpretHql(AbstractSharedSessionContract.java:744)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:794)
	... 92 common frames omitted
2025-06-12 11:00:36,377 INFO o.s.b.StartupInfoLogger [main] Starting BusinessApplication using Java 23.0.1 with PID 28228 (C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes started by AKA665 in C:\Users\<USER>\Documents\SD1 SDY)
2025-06-12 11:00:36,381 INFO o.s.b.SpringApplication [main] No active profile set, falling back to 1 default profile: "default"
2025-06-12 11:00:41,829 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-12 11:00:44,130 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Finished Spring Data repository scanning in 2287 ms. Found 297 JPA repository interfaces.
2025-06-12 11:00:46,238 INFO o.s.b.w.e.t.TomcatWebServer [main] Tomcat initialized with port 8095 (http)
2025-06-12 11:00:46,264 INFO o.a.j.l.DirectJDKLog [main] Initializing ProtocolHandler ["http-nio-8095"]
2025-06-12 11:00:46,274 INFO o.a.j.l.DirectJDKLog [main] Starting service [Tomcat]
2025-06-12 11:00:46,274 INFO o.a.j.l.DirectJDKLog [main] Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-12 11:00:46,432 INFO o.a.j.l.DirectJDKLog [main] Initializing Spring embedded WebApplicationContext
2025-06-12 11:00:46,432 INFO o.s.b.w.s.c.ServletWebServerApplicationContext [main] Root WebApplicationContext: initialization completed in 9958 ms
2025-06-12 11:00:48,581 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Starting...
2025-06-12 11:00:52,250 INFO c.z.h.p.HikariPool [main] HikariPool-1 - Added connection ConnectionID:1 ClientConnectionId: 66d39af9-1c88-4fac-a1bb-2f541eb988f2
2025-06-12 11:00:52,252 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Start completed.
2025-06-12 11:00:52,336 INFO o.h.j.i.u.LogHelper [main] HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-12 11:00:52,427 INFO o.h.Version [main] HHH000412: Hibernate ORM core version 6.3.1.Final
2025-06-12 11:00:52,508 INFO o.h.c.i.RegionFactoryInitiator [main] HHH000026: Second-level cache disabled
2025-06-12 11:00:53,028 INFO o.s.o.j.p.SpringPersistenceUnitInfo [main] No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-12 11:00:56,379 WARN o.h.m.RootClass [main] HHH000038: Composite-id class does not override equals(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-12 11:00:56,379 WARN o.h.m.RootClass [main] HHH000039: Composite-id class does not override hashCode(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-12 11:00:59,824 INFO o.h.e.t.j.p.i.JtaPlatformInitiator [main] HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-12 11:00:59,862 INFO o.s.o.j.AbstractEntityManagerFactoryBean [main] Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-12 11:01:00,767 INFO o.s.d.j.r.q.QueryEnhancerFactory [main] Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-12 11:01:04,932 WARN o.s.c.s.AbstractApplicationContext [main] Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'planificacionCambioZonaUseCaseImpl' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\business\core\planing\usecase\planificacion\PlanificacionCambioZonaUseCaseImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'planingConfig': Unsatisfied dependency expressed through field 'findMostAvailableFullContainerGateOutService': Error creating bean with name 'findMostAvailableFullContainerGateOutService' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\sdy\service\FindMostAvailableFullContainerGateOutService.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'movementInstructionRepository' defined in com.maersk.sd1.common.repository.MovementInstructionRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findContainerInfoByIds(java.util.List,java.lang.Integer); Reason: Validation failed for query for method public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findContainerInfoByIds(java.util.List,java.lang.Integer)
2025-06-12 11:01:04,932 INFO o.s.o.j.AbstractEntityManagerFactoryBean [main] Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-12 11:01:04,941 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Shutdown initiated...
2025-06-12 11:01:05,892 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Shutdown completed.
2025-06-12 11:01:06,895 WARN z.r.i.AsyncReporter$BoundedAsyncReporter [main] Timed out waiting for in-flight spans to send
2025-06-12 11:01:06,902 INFO o.a.j.l.DirectJDKLog [main] Stopping service [Tomcat]
2025-06-12 11:01:06,928 INFO o.s.b.a.l.ConditionEvaluationReportLogger [main] 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-06-12 11:01:06,951 ERROR o.s.b.SpringApplication [main] Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'planificacionCambioZonaUseCaseImpl' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\business\core\planing\usecase\planificacion\PlanificacionCambioZonaUseCaseImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'planingConfig': Unsatisfied dependency expressed through field 'findMostAvailableFullContainerGateOutService': Error creating bean with name 'findMostAvailableFullContainerGateOutService' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\sdy\service\FindMostAvailableFullContainerGateOutService.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'movementInstructionRepository' defined in com.maersk.sd1.common.repository.MovementInstructionRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findContainerInfoByIds(java.util.List,java.lang.Integer); Reason: Validation failed for query for method public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findContainerInfoByIds(java.util.List,java.lang.Integer)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:795)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1212)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.maersk.sd1.BusinessApplication.main(BusinessApplication.java:13)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'planingConfig': Unsatisfied dependency expressed through field 'findMostAvailableFullContainerGateOutService': Error creating bean with name 'findMostAvailableFullContainerGateOutService' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\sdy\service\FindMostAvailableFullContainerGateOutService.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'movementInstructionRepository' defined in com.maersk.sd1.common.repository.MovementInstructionRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findContainerInfoByIds(java.util.List,java.lang.Integer); Reason: Validation failed for query for method public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findContainerInfoByIds(java.util.List,java.lang.Integer)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:409)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1355)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1185)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:782)
	... 19 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'findMostAvailableFullContainerGateOutService' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\sdy\service\FindMostAvailableFullContainerGateOutService.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'movementInstructionRepository' defined in com.maersk.sd1.common.repository.MovementInstructionRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findContainerInfoByIds(java.util.List,java.lang.Integer); Reason: Validation failed for query for method public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findContainerInfoByIds(java.util.List,java.lang.Integer)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:795)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1212)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 43 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'movementInstructionRepository' defined in com.maersk.sd1.common.repository.MovementInstructionRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findContainerInfoByIds(java.util.List,java.lang.Integer); Reason: Validation failed for query for method public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findContainerInfoByIds(java.util.List,java.lang.Integer)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1806)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:782)
	... 56 common frames omitted
Caused by: org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findContainerInfoByIds(java.util.List,java.lang.Integer); Reason: Validation failed for query for method public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findContainerInfoByIds(java.util.List,java.lang.Integer)
	at org.springframework.data.repository.query.QueryCreationException.create(QueryCreationException.java:101)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:119)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.mapMethodsToQuery(QueryExecutorMethodInterceptor.java:103)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lambda$new$0(QueryExecutorMethodInterceptor.java:92)
	at java.base/java.util.Optional.map(Optional.java:260)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.<init>(QueryExecutorMethodInterceptor.java:92)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport.getRepository(RepositoryFactorySupport.java:357)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.lambda$afterPropertiesSet$5(RepositoryFactoryBeanSupport.java:290)
	at org.springframework.data.util.Lazy.getNullable(Lazy.java:135)
	at org.springframework.data.util.Lazy.get(Lazy.java:113)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.afterPropertiesSet(RepositoryFactoryBeanSupport.java:296)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean.afterPropertiesSet(JpaRepositoryFactoryBean.java:132)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1802)
	... 67 common frames omitted
Caused by: java.lang.IllegalArgumentException: Validation failed for query for method public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findContainerInfoByIds(java.util.List,java.lang.Integer)
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.validateQuery(SimpleJpaQuery.java:100)
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.<init>(SimpleJpaQuery.java:70)
	at org.springframework.data.jpa.repository.query.JpaQueryFactory.fromMethodWithQueryString(JpaQueryFactory.java:60)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$DeclaredQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:170)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateIfNotFoundQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:252)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$AbstractQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:95)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:115)
	... 79 common frames omitted
Caused by: java.lang.IllegalArgumentException: org.hibernate.query.SyntaxException: At 1:333 and token 'NULL', no viable alternative at input 'SELECT new com.maersk.sd1.sdy.service.PendingPlanningInstructionService$ContainerInfoDTO(cnt.id, cnt.containerNumber, cnt.catSize.id, catSize.code, cnt.catFamily.id, catFamily.description, catGrade.description, cnt.catContainerType.id, catType.description, cnt.catGrade.id, COALESCE(smty.gateInEir.id, sfll.gateInEir.id), NULL, NULL *NULL FROM Container cnt LEFT JOIN cnt.catSize catSize LEFT JOIN cnt.catFamily catFamily LEFT JOIN cnt.catGrade catGrade LEFT JOIN cnt.catContainerType catType LEFT JOIN StockEmpty smty ON (smty.container.id = cnt.id AND smty.inStock = true AND smty.active = true AND smty.subBusinessUnit.id = :subBusinessUnitId) LEFT JOIN StockFull sfll ON (sfll.container.id = cnt.id AND sfll.inStock = true AND sfll.active = true AND sfll.subBusinessUnit.id = :subBusinessUnitId) WHERE cnt.id IN :containerIds' [SELECT new com.maersk.sd1.sdy.service.PendingPlanningInstructionService$ContainerInfoDTO(cnt.id, cnt.containerNumber, cnt.catSize.id, catSize.code, cnt.catFamily.id, catFamily.description, catGrade.description, cnt.catContainerType.id, catType.description, cnt.catGrade.id, COALESCE(smty.gateInEir.id, sfll.gateInEir.id), NULL, NULL NULL FROM Container cnt LEFT JOIN cnt.catSize catSize LEFT JOIN cnt.catFamily catFamily LEFT JOIN cnt.catGrade catGrade LEFT JOIN cnt.catContainerType catType LEFT JOIN StockEmpty smty ON (smty.container.id = cnt.id AND smty.inStock = true AND smty.active = true AND smty.subBusinessUnit.id = :subBusinessUnitId) LEFT JOIN StockFull sfll ON (sfll.container.id = cnt.id AND sfll.inStock = true AND sfll.active = true AND sfll.subBusinessUnit.id = :subBusinessUnitId) WHERE cnt.id IN :containerIds]
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:143)
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:167)
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:173)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:802)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:707)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:132)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.orm.jpa.ExtendedEntityManagerCreator$ExtendedEntityManagerInvocationHandler.invoke(ExtendedEntityManagerCreator.java:364)
	at jdk.proxy2/jdk.proxy2.$Proxy172.createQuery(Unknown Source)
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.validateQuery(SimpleJpaQuery.java:94)
	... 85 common frames omitted
Caused by: org.hibernate.query.SyntaxException: At 1:333 and token 'NULL', no viable alternative at input 'SELECT new com.maersk.sd1.sdy.service.PendingPlanningInstructionService$ContainerInfoDTO(cnt.id, cnt.containerNumber, cnt.catSize.id, catSize.code, cnt.catFamily.id, catFamily.description, catGrade.description, cnt.catContainerType.id, catType.description, cnt.catGrade.id, COALESCE(smty.gateInEir.id, sfll.gateInEir.id), NULL, NULL *NULL FROM Container cnt LEFT JOIN cnt.catSize catSize LEFT JOIN cnt.catFamily catFamily LEFT JOIN cnt.catGrade catGrade LEFT JOIN cnt.catContainerType catType LEFT JOIN StockEmpty smty ON (smty.container.id = cnt.id AND smty.inStock = true AND smty.active = true AND smty.subBusinessUnit.id = :subBusinessUnitId) LEFT JOIN StockFull sfll ON (sfll.container.id = cnt.id AND sfll.inStock = true AND sfll.active = true AND sfll.subBusinessUnit.id = :subBusinessUnitId) WHERE cnt.id IN :containerIds' [SELECT new com.maersk.sd1.sdy.service.PendingPlanningInstructionService$ContainerInfoDTO(cnt.id, cnt.containerNumber, cnt.catSize.id, catSize.code, cnt.catFamily.id, catFamily.description, catGrade.description, cnt.catContainerType.id, catType.description, cnt.catGrade.id, COALESCE(smty.gateInEir.id, sfll.gateInEir.id), NULL, NULL NULL FROM Container cnt LEFT JOIN cnt.catSize catSize LEFT JOIN cnt.catFamily catFamily LEFT JOIN cnt.catGrade catGrade LEFT JOIN cnt.catContainerType catType LEFT JOIN StockEmpty smty ON (smty.container.id = cnt.id AND smty.inStock = true AND smty.active = true AND smty.subBusinessUnit.id = :subBusinessUnitId) LEFT JOIN StockFull sfll ON (sfll.container.id = cnt.id AND sfll.inStock = true AND sfll.active = true AND sfll.subBusinessUnit.id = :subBusinessUnitId) WHERE cnt.id IN :containerIds]
	at org.hibernate.query.hql.internal.StandardHqlTranslator$1.syntaxError(StandardHqlTranslator.java:108)
	at org.antlr.v4.runtime.ProxyErrorListener.syntaxError(ProxyErrorListener.java:41)
	at org.antlr.v4.runtime.Parser.notifyErrorListeners(Parser.java:543)
	at org.antlr.v4.runtime.DefaultErrorStrategy.reportNoViableAlternative(DefaultErrorStrategy.java:310)
	at org.antlr.v4.runtime.DefaultErrorStrategy.reportError(DefaultErrorStrategy.java:136)
	at org.hibernate.grammars.hql.HqlParser.queryExpression(HqlParser.java:1787)
	at org.hibernate.grammars.hql.HqlParser.selectStatement(HqlParser.java:405)
	at org.hibernate.grammars.hql.HqlParser.statement(HqlParser.java:336)
	at org.hibernate.query.hql.internal.StandardHqlTranslator.parseHql(StandardHqlTranslator.java:132)
	at org.hibernate.query.hql.internal.StandardHqlTranslator.translate(StandardHqlTranslator.java:67)
	at org.hibernate.query.internal.QueryInterpretationCacheStandardImpl.createHqlInterpretation(QueryInterpretationCacheStandardImpl.java:165)
	at org.hibernate.query.internal.QueryInterpretationCacheStandardImpl.resolveHqlInterpretation(QueryInterpretationCacheStandardImpl.java:147)
	at org.hibernate.internal.AbstractSharedSessionContract.interpretHql(AbstractSharedSessionContract.java:744)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:794)
	... 92 common frames omitted
2025-06-12 11:04:08,621 INFO o.s.b.StartupInfoLogger [main] Starting BusinessApplication using Java 23.0.1 with PID 524 (C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes started by AKA665 in C:\Users\<USER>\Documents\SD1 SDY)
2025-06-12 11:04:08,624 INFO o.s.b.SpringApplication [main] No active profile set, falling back to 1 default profile: "default"
2025-06-12 11:04:13,659 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-12 11:04:15,882 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Finished Spring Data repository scanning in 2213 ms. Found 297 JPA repository interfaces.
2025-06-12 11:04:17,888 INFO o.s.b.w.e.t.TomcatWebServer [main] Tomcat initialized with port 8095 (http)
2025-06-12 11:04:17,908 INFO o.a.j.l.DirectJDKLog [main] Initializing ProtocolHandler ["http-nio-8095"]
2025-06-12 11:04:17,918 INFO o.a.j.l.DirectJDKLog [main] Starting service [Tomcat]
2025-06-12 11:04:17,918 INFO o.a.j.l.DirectJDKLog [main] Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-12 11:04:18,115 INFO o.a.j.l.DirectJDKLog [main] Initializing Spring embedded WebApplicationContext
2025-06-12 11:04:18,120 INFO o.s.b.w.s.c.ServletWebServerApplicationContext [main] Root WebApplicationContext: initialization completed in 9409 ms
2025-06-12 11:04:19,914 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Starting...
2025-06-12 11:04:22,885 INFO c.z.h.p.HikariPool [main] HikariPool-1 - Added connection ConnectionID:1 ClientConnectionId: 3ddd710b-a88e-4ebe-9f55-8aab8ab02848
2025-06-12 11:04:22,890 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Start completed.
2025-06-12 11:04:22,970 INFO o.h.j.i.u.LogHelper [main] HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-12 11:04:23,038 INFO o.h.Version [main] HHH000412: Hibernate ORM core version 6.3.1.Final
2025-06-12 11:04:23,070 INFO o.h.c.i.RegionFactoryInitiator [main] HHH000026: Second-level cache disabled
2025-06-12 11:04:23,538 INFO o.s.o.j.p.SpringPersistenceUnitInfo [main] No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-12 11:04:26,466 WARN o.h.m.RootClass [main] HHH000038: Composite-id class does not override equals(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-12 11:04:26,466 WARN o.h.m.RootClass [main] HHH000039: Composite-id class does not override hashCode(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-12 11:04:30,241 INFO o.h.e.t.j.p.i.JtaPlatformInitiator [main] HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-12 11:04:30,277 INFO o.s.o.j.AbstractEntityManagerFactoryBean [main] Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-12 11:04:31,043 INFO o.s.d.j.r.q.QueryEnhancerFactory [main] Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-12 11:04:35,026 WARN o.s.c.s.AbstractApplicationContext [main] Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'planificacionCambioZonaUseCaseImpl' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\business\core\planing\usecase\planificacion\PlanificacionCambioZonaUseCaseImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'planingConfig': Unsatisfied dependency expressed through field 'findMostAvailableFullContainerGateOutService': Error creating bean with name 'findMostAvailableFullContainerGateOutService' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\sdy\service\FindMostAvailableFullContainerGateOutService.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'movementInstructionRepository' defined in com.maersk.sd1.common.repository.MovementInstructionRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findContainerInfoByIds(java.util.List,java.lang.Integer); Reason: Validation failed for query for method public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findContainerInfoByIds(java.util.List,java.lang.Integer)
2025-06-12 11:04:35,029 INFO o.s.o.j.AbstractEntityManagerFactoryBean [main] Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-12 11:04:35,034 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Shutdown initiated...
2025-06-12 11:04:35,133 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Shutdown completed.
2025-06-12 11:04:36,140 WARN z.r.i.AsyncReporter$BoundedAsyncReporter [main] Timed out waiting for in-flight spans to send
2025-06-12 11:04:36,140 INFO o.a.j.l.DirectJDKLog [main] Stopping service [Tomcat]
2025-06-12 11:04:36,160 INFO o.s.b.a.l.ConditionEvaluationReportLogger [main] 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-06-12 11:04:36,184 ERROR o.s.b.SpringApplication [main] Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'planificacionCambioZonaUseCaseImpl' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\business\core\planing\usecase\planificacion\PlanificacionCambioZonaUseCaseImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'planingConfig': Unsatisfied dependency expressed through field 'findMostAvailableFullContainerGateOutService': Error creating bean with name 'findMostAvailableFullContainerGateOutService' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\sdy\service\FindMostAvailableFullContainerGateOutService.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'movementInstructionRepository' defined in com.maersk.sd1.common.repository.MovementInstructionRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findContainerInfoByIds(java.util.List,java.lang.Integer); Reason: Validation failed for query for method public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findContainerInfoByIds(java.util.List,java.lang.Integer)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:795)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1212)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.maersk.sd1.BusinessApplication.main(BusinessApplication.java:13)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'planingConfig': Unsatisfied dependency expressed through field 'findMostAvailableFullContainerGateOutService': Error creating bean with name 'findMostAvailableFullContainerGateOutService' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\sdy\service\FindMostAvailableFullContainerGateOutService.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'movementInstructionRepository' defined in com.maersk.sd1.common.repository.MovementInstructionRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findContainerInfoByIds(java.util.List,java.lang.Integer); Reason: Validation failed for query for method public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findContainerInfoByIds(java.util.List,java.lang.Integer)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:409)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1355)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1185)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:782)
	... 19 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'findMostAvailableFullContainerGateOutService' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\sdy\service\FindMostAvailableFullContainerGateOutService.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'movementInstructionRepository' defined in com.maersk.sd1.common.repository.MovementInstructionRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findContainerInfoByIds(java.util.List,java.lang.Integer); Reason: Validation failed for query for method public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findContainerInfoByIds(java.util.List,java.lang.Integer)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:795)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1212)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 43 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'movementInstructionRepository' defined in com.maersk.sd1.common.repository.MovementInstructionRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findContainerInfoByIds(java.util.List,java.lang.Integer); Reason: Validation failed for query for method public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findContainerInfoByIds(java.util.List,java.lang.Integer)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1806)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:782)
	... 56 common frames omitted
Caused by: org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findContainerInfoByIds(java.util.List,java.lang.Integer); Reason: Validation failed for query for method public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findContainerInfoByIds(java.util.List,java.lang.Integer)
	at org.springframework.data.repository.query.QueryCreationException.create(QueryCreationException.java:101)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:119)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.mapMethodsToQuery(QueryExecutorMethodInterceptor.java:103)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lambda$new$0(QueryExecutorMethodInterceptor.java:92)
	at java.base/java.util.Optional.map(Optional.java:260)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.<init>(QueryExecutorMethodInterceptor.java:92)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport.getRepository(RepositoryFactorySupport.java:357)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.lambda$afterPropertiesSet$5(RepositoryFactoryBeanSupport.java:290)
	at org.springframework.data.util.Lazy.getNullable(Lazy.java:135)
	at org.springframework.data.util.Lazy.get(Lazy.java:113)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.afterPropertiesSet(RepositoryFactoryBeanSupport.java:296)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean.afterPropertiesSet(JpaRepositoryFactoryBean.java:132)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1802)
	... 67 common frames omitted
Caused by: java.lang.IllegalArgumentException: Validation failed for query for method public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findContainerInfoByIds(java.util.List,java.lang.Integer)
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.validateQuery(SimpleJpaQuery.java:100)
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.<init>(SimpleJpaQuery.java:70)
	at org.springframework.data.jpa.repository.query.JpaQueryFactory.fromMethodWithQueryString(JpaQueryFactory.java:60)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$DeclaredQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:170)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateIfNotFoundQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:252)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$AbstractQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:95)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:115)
	... 79 common frames omitted
Caused by: java.lang.IllegalArgumentException: org.hibernate.query.SyntaxException: At 1:370 and token '(', mismatched input '(', expecting one of the following tokens: <EOF>, ',', FROM, GROUP, ORDER, WHERE [SELECT new com.maersk.sd1.sdy.service.PendingPlanningInstructionService$ContainerInfoDTO(cnt.id, cnt.containerNumber, cnt.catSize.id, catSize.code, cnt.catFamily.id, catFamily.description, catGrade.description, cnt.catContainerType.id, catType.description, cnt.catGrade.id, COALESCE(smty.gateInEir.id, sfll.gateInEir.id), CAST(NULL AS string), CAST(NULL AS string)) CAST(NULL AS string)) FROM Container cnt LEFT JOIN cnt.catSize catSize LEFT JOIN cnt.catFamily catFamily LEFT JOIN cnt.catGrade catGrade LEFT JOIN cnt.catContainerType catType LEFT JOIN StockEmpty smty ON (smty.container.id = cnt.id AND smty.inStock = true AND smty.active = true AND smty.subBusinessUnit.id = :subBusinessUnitId) LEFT JOIN StockFull sfll ON (sfll.container.id = cnt.id AND sfll.inStock = true AND sfll.active = true AND sfll.subBusinessUnit.id = :subBusinessUnitId) WHERE cnt.id IN :containerIds]
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:143)
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:167)
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:173)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:802)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:707)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:132)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.orm.jpa.ExtendedEntityManagerCreator$ExtendedEntityManagerInvocationHandler.invoke(ExtendedEntityManagerCreator.java:364)
	at jdk.proxy2/jdk.proxy2.$Proxy172.createQuery(Unknown Source)
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.validateQuery(SimpleJpaQuery.java:94)
	... 85 common frames omitted
Caused by: org.hibernate.query.SyntaxException: At 1:370 and token '(', mismatched input '(', expecting one of the following tokens: <EOF>, ',', FROM, GROUP, ORDER, WHERE [SELECT new com.maersk.sd1.sdy.service.PendingPlanningInstructionService$ContainerInfoDTO(cnt.id, cnt.containerNumber, cnt.catSize.id, catSize.code, cnt.catFamily.id, catFamily.description, catGrade.description, cnt.catContainerType.id, catType.description, cnt.catGrade.id, COALESCE(smty.gateInEir.id, sfll.gateInEir.id), CAST(NULL AS string), CAST(NULL AS string)) CAST(NULL AS string)) FROM Container cnt LEFT JOIN cnt.catSize catSize LEFT JOIN cnt.catFamily catFamily LEFT JOIN cnt.catGrade catGrade LEFT JOIN cnt.catContainerType catType LEFT JOIN StockEmpty smty ON (smty.container.id = cnt.id AND smty.inStock = true AND smty.active = true AND smty.subBusinessUnit.id = :subBusinessUnitId) LEFT JOIN StockFull sfll ON (sfll.container.id = cnt.id AND sfll.inStock = true AND sfll.active = true AND sfll.subBusinessUnit.id = :subBusinessUnitId) WHERE cnt.id IN :containerIds]
	at org.hibernate.query.hql.internal.StandardHqlTranslator$1.syntaxError(StandardHqlTranslator.java:108)
	at org.antlr.v4.runtime.ProxyErrorListener.syntaxError(ProxyErrorListener.java:41)
	at org.antlr.v4.runtime.Parser.notifyErrorListeners(Parser.java:543)
	at org.antlr.v4.runtime.DefaultErrorStrategy.reportInputMismatch(DefaultErrorStrategy.java:327)
	at org.antlr.v4.runtime.DefaultErrorStrategy.reportError(DefaultErrorStrategy.java:139)
	at org.hibernate.grammars.hql.HqlParser.statement(HqlParser.java:366)
	at org.hibernate.query.hql.internal.StandardHqlTranslator.parseHql(StandardHqlTranslator.java:143)
	at org.hibernate.query.hql.internal.StandardHqlTranslator.translate(StandardHqlTranslator.java:67)
	at org.hibernate.query.internal.QueryInterpretationCacheStandardImpl.createHqlInterpretation(QueryInterpretationCacheStandardImpl.java:165)
	at org.hibernate.query.internal.QueryInterpretationCacheStandardImpl.resolveHqlInterpretation(QueryInterpretationCacheStandardImpl.java:147)
	at org.hibernate.internal.AbstractSharedSessionContract.interpretHql(AbstractSharedSessionContract.java:744)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:794)
	... 92 common frames omitted
2025-06-12 11:06:25,558 INFO o.s.b.StartupInfoLogger [main] Starting BusinessApplication using Java 23.0.1 with PID 30412 (C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes started by AKA665 in C:\Users\<USER>\Documents\SD1 SDY)
2025-06-12 11:06:25,558 INFO o.s.b.SpringApplication [main] No active profile set, falling back to 1 default profile: "default"
2025-06-12 11:06:31,067 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-12 11:06:33,556 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Finished Spring Data repository scanning in 2476 ms. Found 297 JPA repository interfaces.
2025-06-12 11:06:35,732 INFO o.s.b.w.e.t.TomcatWebServer [main] Tomcat initialized with port 8095 (http)
2025-06-12 11:06:35,752 INFO o.a.j.l.DirectJDKLog [main] Initializing ProtocolHandler ["http-nio-8095"]
2025-06-12 11:06:35,760 INFO o.a.j.l.DirectJDKLog [main] Starting service [Tomcat]
2025-06-12 11:06:35,761 INFO o.a.j.l.DirectJDKLog [main] Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-12 11:06:35,927 INFO o.a.j.l.DirectJDKLog [main] Initializing Spring embedded WebApplicationContext
2025-06-12 11:06:35,927 INFO o.s.b.w.s.c.ServletWebServerApplicationContext [main] Root WebApplicationContext: initialization completed in 10257 ms
2025-06-12 11:06:37,901 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Starting...
2025-06-12 11:06:40,682 INFO c.z.h.p.HikariPool [main] HikariPool-1 - Added connection ConnectionID:1 ClientConnectionId: 63245b7f-fa64-4344-bf3f-62421064bd1c
2025-06-12 11:06:40,688 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Start completed.
2025-06-12 11:06:40,771 INFO o.h.j.i.u.LogHelper [main] HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-12 11:06:40,869 INFO o.h.Version [main] HHH000412: Hibernate ORM core version 6.3.1.Final
2025-06-12 11:06:40,939 INFO o.h.c.i.RegionFactoryInitiator [main] HHH000026: Second-level cache disabled
2025-06-12 11:06:41,333 INFO o.s.o.j.p.SpringPersistenceUnitInfo [main] No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-12 11:06:44,461 WARN o.h.m.RootClass [main] HHH000038: Composite-id class does not override equals(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-12 11:06:44,464 WARN o.h.m.RootClass [main] HHH000039: Composite-id class does not override hashCode(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-12 11:06:48,166 INFO o.h.e.t.j.p.i.JtaPlatformInitiator [main] HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-12 11:06:48,200 INFO o.s.o.j.AbstractEntityManagerFactoryBean [main] Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-12 11:06:49,212 INFO o.s.d.j.r.q.QueryEnhancerFactory [main] Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-12 11:06:54,067 WARN o.s.c.s.AbstractApplicationContext [main] Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'planificacionCambioZonaUseCaseImpl' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\business\core\planing\usecase\planificacion\PlanificacionCambioZonaUseCaseImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'planingConfig': Unsatisfied dependency expressed through field 'findMostAvailableEmptyContainerGateOutService': Error creating bean with name 'findMostAvailableEmptyContainerGateOutService' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\sdy\service\FindMostAvailableEmptyContainerGateOutService.class]: Unsatisfied dependency expressed through constructor parameter 2: Error creating bean with name 'eirRepository' defined in com.maersk.sd1.common.repository.EirRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.maersk.sd1.common.repository.EirRepository.findEmptyFullIdsByEirIds(java.util.List); Reason: Validation failed for query for method public abstract java.util.List com.maersk.sd1.common.repository.EirRepository.findEmptyFullIdsByEirIds(java.util.List)
2025-06-12 11:06:54,067 INFO o.s.o.j.AbstractEntityManagerFactoryBean [main] Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-12 11:06:54,067 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Shutdown initiated...
2025-06-12 11:06:56,574 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Shutdown completed.
2025-06-12 11:06:57,579 WARN z.r.i.AsyncReporter$BoundedAsyncReporter [main] Timed out waiting for in-flight spans to send
2025-06-12 11:06:57,584 INFO o.a.j.l.DirectJDKLog [main] Stopping service [Tomcat]
2025-06-12 11:06:57,603 INFO o.s.b.a.l.ConditionEvaluationReportLogger [main] 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-06-12 11:06:57,636 ERROR o.s.b.SpringApplication [main] Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'planificacionCambioZonaUseCaseImpl' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\business\core\planing\usecase\planificacion\PlanificacionCambioZonaUseCaseImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'planingConfig': Unsatisfied dependency expressed through field 'findMostAvailableEmptyContainerGateOutService': Error creating bean with name 'findMostAvailableEmptyContainerGateOutService' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\sdy\service\FindMostAvailableEmptyContainerGateOutService.class]: Unsatisfied dependency expressed through constructor parameter 2: Error creating bean with name 'eirRepository' defined in com.maersk.sd1.common.repository.EirRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.maersk.sd1.common.repository.EirRepository.findEmptyFullIdsByEirIds(java.util.List); Reason: Validation failed for query for method public abstract java.util.List com.maersk.sd1.common.repository.EirRepository.findEmptyFullIdsByEirIds(java.util.List)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:795)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1212)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.maersk.sd1.BusinessApplication.main(BusinessApplication.java:13)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'planingConfig': Unsatisfied dependency expressed through field 'findMostAvailableEmptyContainerGateOutService': Error creating bean with name 'findMostAvailableEmptyContainerGateOutService' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\sdy\service\FindMostAvailableEmptyContainerGateOutService.class]: Unsatisfied dependency expressed through constructor parameter 2: Error creating bean with name 'eirRepository' defined in com.maersk.sd1.common.repository.EirRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.maersk.sd1.common.repository.EirRepository.findEmptyFullIdsByEirIds(java.util.List); Reason: Validation failed for query for method public abstract java.util.List com.maersk.sd1.common.repository.EirRepository.findEmptyFullIdsByEirIds(java.util.List)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:409)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1355)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1185)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:782)
	... 19 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'findMostAvailableEmptyContainerGateOutService' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\sdy\service\FindMostAvailableEmptyContainerGateOutService.class]: Unsatisfied dependency expressed through constructor parameter 2: Error creating bean with name 'eirRepository' defined in com.maersk.sd1.common.repository.EirRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.maersk.sd1.common.repository.EirRepository.findEmptyFullIdsByEirIds(java.util.List); Reason: Validation failed for query for method public abstract java.util.List com.maersk.sd1.common.repository.EirRepository.findEmptyFullIdsByEirIds(java.util.List)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:795)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1212)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 43 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'eirRepository' defined in com.maersk.sd1.common.repository.EirRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.maersk.sd1.common.repository.EirRepository.findEmptyFullIdsByEirIds(java.util.List); Reason: Validation failed for query for method public abstract java.util.List com.maersk.sd1.common.repository.EirRepository.findEmptyFullIdsByEirIds(java.util.List)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1806)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:782)
	... 56 common frames omitted
Caused by: org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract java.util.List com.maersk.sd1.common.repository.EirRepository.findEmptyFullIdsByEirIds(java.util.List); Reason: Validation failed for query for method public abstract java.util.List com.maersk.sd1.common.repository.EirRepository.findEmptyFullIdsByEirIds(java.util.List)
	at org.springframework.data.repository.query.QueryCreationException.create(QueryCreationException.java:101)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:119)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.mapMethodsToQuery(QueryExecutorMethodInterceptor.java:103)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lambda$new$0(QueryExecutorMethodInterceptor.java:92)
	at java.base/java.util.Optional.map(Optional.java:260)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.<init>(QueryExecutorMethodInterceptor.java:92)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport.getRepository(RepositoryFactorySupport.java:357)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.lambda$afterPropertiesSet$5(RepositoryFactoryBeanSupport.java:290)
	at org.springframework.data.util.Lazy.getNullable(Lazy.java:135)
	at org.springframework.data.util.Lazy.get(Lazy.java:113)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.afterPropertiesSet(RepositoryFactoryBeanSupport.java:296)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean.afterPropertiesSet(JpaRepositoryFactoryBean.java:132)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1802)
	... 67 common frames omitted
Caused by: java.lang.IllegalArgumentException: Validation failed for query for method public abstract java.util.List com.maersk.sd1.common.repository.EirRepository.findEmptyFullIdsByEirIds(java.util.List)
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.validateQuery(SimpleJpaQuery.java:100)
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.<init>(SimpleJpaQuery.java:70)
	at org.springframework.data.jpa.repository.query.JpaQueryFactory.fromMethodWithQueryString(JpaQueryFactory.java:60)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$DeclaredQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:170)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateIfNotFoundQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:252)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$AbstractQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:95)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:115)
	... 79 common frames omitted
Caused by: java.lang.IllegalArgumentException: org.hibernate.query.sqm.UnknownPathException: Could not resolve attribute 'eirId' of 'com.maersk.sd1.common.model.Eir' [    SELECT e.eirId, e.catEmptyFullId
    FROM Eir e
    WHERE e.eirId IN :eirIds
]
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:143)
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:167)
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:173)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:802)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:707)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:132)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.orm.jpa.ExtendedEntityManagerCreator$ExtendedEntityManagerInvocationHandler.invoke(ExtendedEntityManagerCreator.java:364)
	at jdk.proxy2/jdk.proxy2.$Proxy172.createQuery(Unknown Source)
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.validateQuery(SimpleJpaQuery.java:94)
	... 85 common frames omitted
Caused by: org.hibernate.query.sqm.UnknownPathException: Could not resolve attribute 'eirId' of 'com.maersk.sd1.common.model.Eir' [    SELECT e.eirId, e.catEmptyFullId
    FROM Eir e
    WHERE e.eirId IN :eirIds
]
	at org.hibernate.query.hql.internal.StandardHqlTranslator.translate(StandardHqlTranslator.java:87)
	at org.hibernate.query.internal.QueryInterpretationCacheStandardImpl.createHqlInterpretation(QueryInterpretationCacheStandardImpl.java:165)
	at org.hibernate.query.internal.QueryInterpretationCacheStandardImpl.resolveHqlInterpretation(QueryInterpretationCacheStandardImpl.java:147)
	at org.hibernate.internal.AbstractSharedSessionContract.interpretHql(AbstractSharedSessionContract.java:744)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:794)
	... 92 common frames omitted
Caused by: org.hibernate.query.sqm.PathElementException: Could not resolve attribute 'eirId' of 'com.maersk.sd1.common.model.Eir'
	at org.hibernate.query.sqm.SqmPathSource.getSubPathSource(SqmPathSource.java:95)
	at org.hibernate.query.sqm.tree.domain.AbstractSqmPath.get(AbstractSqmPath.java:195)
	at org.hibernate.query.sqm.tree.domain.AbstractSqmFrom.resolvePathPart(AbstractSqmFrom.java:196)
	at org.hibernate.query.hql.internal.DomainPathPart.resolvePathPart(DomainPathPart.java:42)
	at org.hibernate.query.hql.internal.BasicDotIdentifierConsumer.consumeIdentifier(BasicDotIdentifierConsumer.java:91)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitSimplePath(SemanticQueryBuilder.java:5010)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitIndexedPathAccessFragment(SemanticQueryBuilder.java:4959)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitGeneralPathFragment(SemanticQueryBuilder.java:4934)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitGeneralPathExpression(SemanticQueryBuilder.java:1775)
	at org.hibernate.grammars.hql.HqlParser$GeneralPathExpressionContext.accept(HqlParser.java:7571)
	at org.antlr.v4.runtime.tree.AbstractParseTreeVisitor.visitChildren(AbstractParseTreeVisitor.java:46)
	at org.hibernate.grammars.hql.HqlParserBaseVisitor.visitBarePrimaryExpression(HqlParserBaseVisitor.java:755)
	at org.hibernate.grammars.hql.HqlParser$BarePrimaryExpressionContext.accept(HqlParser.java:7045)
	at org.antlr.v4.runtime.tree.AbstractParseTreeVisitor.visitChildren(AbstractParseTreeVisitor.java:46)
	at org.hibernate.grammars.hql.HqlParserBaseVisitor.visitExpressionOrPredicate(HqlParserBaseVisitor.java:895)
	at org.hibernate.grammars.hql.HqlParser$ExpressionOrPredicateContext.accept(HqlParser.java:7794)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitSelectableNode(SemanticQueryBuilder.java:1331)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitSelection(SemanticQueryBuilder.java:1308)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitSelectClause(SemanticQueryBuilder.java:1301)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitQuery(SemanticQueryBuilder.java:1153)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitQuerySpecExpression(SemanticQueryBuilder.java:940)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitQuerySpecExpression(SemanticQueryBuilder.java:268)
	at org.hibernate.grammars.hql.HqlParser$QuerySpecExpressionContext.accept(HqlParser.java:1844)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitSimpleQueryGroup(SemanticQueryBuilder.java:925)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitSimpleQueryGroup(SemanticQueryBuilder.java:268)
	at org.hibernate.grammars.hql.HqlParser$SimpleQueryGroupContext.accept(HqlParser.java:1718)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitSelectStatement(SemanticQueryBuilder.java:442)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitStatement(SemanticQueryBuilder.java:401)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.buildSemanticModel(SemanticQueryBuilder.java:310)
	at org.hibernate.query.hql.internal.StandardHqlTranslator.translate(StandardHqlTranslator.java:71)
	... 96 common frames omitted
2025-06-12 11:09:02,342 INFO o.s.b.StartupInfoLogger [main] Starting BusinessApplication using Java 23.0.1 with PID 18420 (C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes started by AKA665 in C:\Users\<USER>\Documents\SD1 SDY)
2025-06-12 11:09:02,347 INFO o.s.b.SpringApplication [main] No active profile set, falling back to 1 default profile: "default"
2025-06-12 11:09:14,615 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-12 11:09:18,942 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Finished Spring Data repository scanning in 4301 ms. Found 297 JPA repository interfaces.
2025-06-12 11:09:23,002 INFO o.s.b.w.e.t.TomcatWebServer [main] Tomcat initialized with port 8095 (http)
2025-06-12 11:09:23,038 INFO o.a.j.l.DirectJDKLog [main] Initializing ProtocolHandler ["http-nio-8095"]
2025-06-12 11:09:23,049 INFO o.a.j.l.DirectJDKLog [main] Starting service [Tomcat]
2025-06-12 11:09:23,049 INFO o.a.j.l.DirectJDKLog [main] Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-12 11:09:23,329 INFO o.a.j.l.DirectJDKLog [main] Initializing Spring embedded WebApplicationContext
2025-06-12 11:09:23,334 INFO o.s.b.w.s.c.ServletWebServerApplicationContext [main] Root WebApplicationContext: initialization completed in 20862 ms
2025-06-12 11:09:26,793 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Starting...
2025-06-12 11:09:30,410 INFO c.z.h.p.HikariPool [main] HikariPool-1 - Added connection ConnectionID:1 ClientConnectionId: b4e7ed5a-7c93-454c-9183-3efe65e6313d
2025-06-12 11:09:30,416 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Start completed.
2025-06-12 11:09:30,590 INFO o.h.j.i.u.LogHelper [main] HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-12 11:09:30,761 INFO o.h.Version [main] HHH000412: Hibernate ORM core version 6.3.1.Final
2025-06-12 11:09:30,904 INFO o.h.c.i.RegionFactoryInitiator [main] HHH000026: Second-level cache disabled
2025-06-12 11:09:31,899 INFO o.s.o.j.p.SpringPersistenceUnitInfo [main] No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-12 11:09:35,373 WARN o.h.m.RootClass [main] HHH000038: Composite-id class does not override equals(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-12 11:09:35,373 WARN o.h.m.RootClass [main] HHH000039: Composite-id class does not override hashCode(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-12 11:09:39,207 INFO o.h.e.t.j.p.i.JtaPlatformInitiator [main] HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-12 11:09:39,276 INFO o.s.o.j.AbstractEntityManagerFactoryBean [main] Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-12 11:09:40,251 INFO o.s.d.j.r.q.QueryEnhancerFactory [main] Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-12 11:10:01,275 WARN o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration [main] spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-12 11:10:04,376 INFO o.s.b.a.e.w.EndpointLinksResolver [main] Exposing 1 endpoint beneath base path '/actuator'
2025-06-12 11:10:04,576 INFO o.a.j.l.DirectJDKLog [main] Starting ProtocolHandler ["http-nio-8095"]
2025-06-12 11:10:04,719 INFO o.s.b.w.e.t.TomcatWebServer [main] Tomcat started on port 8095 (http) with context path '/'
2025-06-12 11:10:04,750 INFO o.s.b.StartupInfoLogger [main] Started BusinessApplication in 63.494 seconds (process running for 64.368)
2025-06-12 11:10:15,703 INFO o.a.j.l.DirectJDKLog [http-nio-8095-exec-1] Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-12 11:10:15,705 INFO o.s.w.s.FrameworkServlet [http-nio-8095-exec-1] Initializing Servlet 'dispatcherServlet'
2025-06-12 11:10:15,714 INFO o.s.w.s.FrameworkServlet [http-nio-8095-exec-1] Completed initialization in 9 ms
2025-06-12 11:10:15,897 INFO c.m.s.s.c.PendingPlanningInstructionController [http-nio-8095-exec-1] Request received for patioEditar: PendingPlanningInstructionInput.Root(prefix=PendingPlanningInstructionInput.Prefix(input=PendingPlanningInstructionInput.Input(localBusinessUnitId=87, containerNumber=tevg)))
2025-06-12 11:10:18,342 INFO c.m.s.s.s.PendingPlanningInstructionService [http-nio-8095-exec-1] Catalog IDs: {isGateOut=43083, isEmpty=43081, isContainer=48752}
2025-06-12 11:10:19,148 ERROR c.m.s.s.s.PendingPlanningInstructionService [http-nio-8095-exec-1] Error fetching pending planning instructions: 
org.springframework.dao.InvalidDataAccessApiUsageException: Cannot instantiate class 'com.maersk.sd1.sdy.service.PendingPlanningInstructionService$ContainerInfoDTO' (it has no constructor with signature [java.lang.Integer, java.lang.String, java.lang.Integer, java.lang.String, java.lang.Integer, java.lang.String, java.lang.String, java.lang.Integer, java.lang.String, java.lang.Integer, java.lang.Integer, java.lang.String, java.lang.String], and not every argument has an alias)
	at org.springframework.orm.jpa.EntityManagerFactoryUtils.convertJpaAccessExceptionIfPossible(EntityManagerFactoryUtils.java:368)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:246)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:550)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:335)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:160)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:136)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy184.findContainerInfoByIds(Unknown Source)
	at com.maersk.sd1.sdy.service.PendingPlanningInstructionService.getPendingPlanningInstructions(PendingPlanningInstructionService.java:74)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:379)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:720)
	at com.maersk.sd1.sdy.service.PendingPlanningInstructionService$$SpringCGLIB$$0.getPendingPlanningInstructions(<generated>)
	at com.maersk.sd1.sdy.controller.PendingPlanningInstructionController.editYard(PendingPlanningInstructionController.java:41)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:384)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1741)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1575)
Caused by: java.lang.IllegalStateException: Cannot instantiate class 'com.maersk.sd1.sdy.service.PendingPlanningInstructionService$ContainerInfoDTO' (it has no constructor with signature [java.lang.Integer, java.lang.String, java.lang.Integer, java.lang.String, java.lang.Integer, java.lang.String, java.lang.String, java.lang.Integer, java.lang.String, java.lang.Integer, java.lang.Integer, java.lang.String, java.lang.String], and not every argument has an alias)
	at org.hibernate.sql.results.graph.instantiation.internal.DynamicInstantiationResultImpl.resolveAssembler(DynamicInstantiationResultImpl.java:187)
	at org.hibernate.sql.results.graph.instantiation.internal.DynamicInstantiationResultImpl.createResultAssembler(DynamicInstantiationResultImpl.java:107)
	at org.hibernate.sql.results.jdbc.internal.StandardJdbcValuesMapping.resolveAssemblers(StandardJdbcValuesMapping.java:53)
	at org.hibernate.sql.results.internal.ResultsHelper.createRowReader(ResultsHelper.java:76)
	at org.hibernate.sql.results.internal.ResultsHelper.createRowReader(ResultsHelper.java:62)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.doExecuteQuery(JdbcSelectExecutorStandardImpl.java:340)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.executeQuery(JdbcSelectExecutorStandardImpl.java:168)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.list(JdbcSelectExecutorStandardImpl.java:93)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.list(JdbcSelectExecutor.java:31)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.lambda$new$0(ConcreteSqmSelectQueryPlan.java:109)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.withCacheableSqmInterpretation(ConcreteSqmSelectQueryPlan.java:305)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.performList(ConcreteSqmSelectQueryPlan.java:246)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doList(QuerySqmImpl.java:509)
	at org.hibernate.query.spi.AbstractSelectionQuery.list(AbstractSelectionQuery.java:427)
	at org.hibernate.query.Query.getResultList(Query.java:120)
	at org.springframework.data.jpa.repository.query.JpaQueryExecution$CollectionExecution.doExecute(JpaQueryExecution.java:129)
	at org.springframework.data.jpa.repository.query.JpaQueryExecution.execute(JpaQueryExecution.java:92)
	at org.springframework.data.jpa.repository.query.AbstractJpaQuery.doExecute(AbstractJpaQuery.java:152)
	at org.springframework.data.jpa.repository.query.AbstractJpaQuery.execute(AbstractJpaQuery.java:140)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:169)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:148)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:379)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	... 73 common frames omitted
2025-06-12 11:12:52,360 INFO o.s.o.j.AbstractEntityManagerFactoryBean [SpringApplicationShutdownHook] Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-12 11:12:52,366 INFO c.z.h.HikariDataSource [SpringApplicationShutdownHook] HikariPool-1 - Shutdown initiated...
2025-06-12 11:12:52,376 INFO c.z.h.HikariDataSource [SpringApplicationShutdownHook] HikariPool-1 - Shutdown completed.
2025-06-12 11:13:00,636 INFO o.s.b.StartupInfoLogger [main] Starting BusinessApplication using Java 23.0.1 with PID 13220 (C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes started by AKA665 in C:\Users\<USER>\Documents\SD1 SDY)
2025-06-12 11:13:00,640 INFO o.s.b.SpringApplication [main] No active profile set, falling back to 1 default profile: "default"
2025-06-12 11:13:07,609 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-12 11:13:09,778 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Finished Spring Data repository scanning in 2154 ms. Found 297 JPA repository interfaces.
2025-06-12 11:13:12,258 INFO o.s.b.w.e.t.TomcatWebServer [main] Tomcat initialized with port 8095 (http)
2025-06-12 11:13:12,284 INFO o.a.j.l.DirectJDKLog [main] Initializing ProtocolHandler ["http-nio-8095"]
2025-06-12 11:13:12,293 INFO o.a.j.l.DirectJDKLog [main] Starting service [Tomcat]
2025-06-12 11:13:12,296 INFO o.a.j.l.DirectJDKLog [main] Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-12 11:13:12,487 INFO o.a.j.l.DirectJDKLog [main] Initializing Spring embedded WebApplicationContext
2025-06-12 11:13:12,490 INFO o.s.b.w.s.c.ServletWebServerApplicationContext [main] Root WebApplicationContext: initialization completed in 11730 ms
2025-06-12 11:13:14,676 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Starting...
2025-06-12 11:13:17,256 INFO c.z.h.p.HikariPool [main] HikariPool-1 - Added connection ConnectionID:1 ClientConnectionId: 065847c8-e099-46d9-8fdf-0a05b7bef9bc
2025-06-12 11:13:17,256 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Start completed.
2025-06-12 11:13:17,330 INFO o.h.j.i.u.LogHelper [main] HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-12 11:13:17,407 INFO o.h.Version [main] HHH000412: Hibernate ORM core version 6.3.1.Final
2025-06-12 11:13:17,458 INFO o.h.c.i.RegionFactoryInitiator [main] HHH000026: Second-level cache disabled
2025-06-12 11:13:17,846 INFO o.s.o.j.p.SpringPersistenceUnitInfo [main] No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-12 11:13:20,608 WARN o.h.m.RootClass [main] HHH000038: Composite-id class does not override equals(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-12 11:13:20,615 WARN o.h.m.RootClass [main] HHH000039: Composite-id class does not override hashCode(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-12 11:13:24,526 INFO o.h.e.t.j.p.i.JtaPlatformInitiator [main] HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-12 11:13:24,566 INFO o.s.o.j.AbstractEntityManagerFactoryBean [main] Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-12 11:13:25,409 INFO o.s.d.j.r.q.QueryEnhancerFactory [main] Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-12 11:13:42,620 WARN o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration [main] spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-12 11:13:45,299 INFO o.s.b.a.e.w.EndpointLinksResolver [main] Exposing 1 endpoint beneath base path '/actuator'
2025-06-12 11:13:45,448 INFO o.a.j.l.DirectJDKLog [main] Starting ProtocolHandler ["http-nio-8095"]
2025-06-12 11:13:45,487 INFO o.s.b.w.e.t.TomcatWebServer [main] Tomcat started on port 8095 (http) with context path '/'
2025-06-12 11:13:45,515 INFO o.s.b.StartupInfoLogger [main] Started BusinessApplication in 45.845 seconds (process running for 46.789)
2025-06-12 11:13:49,048 INFO o.a.j.l.DirectJDKLog [http-nio-8095-exec-1] Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-12 11:13:49,048 INFO o.s.w.s.FrameworkServlet [http-nio-8095-exec-1] Initializing Servlet 'dispatcherServlet'
2025-06-12 11:13:49,059 INFO o.s.w.s.FrameworkServlet [http-nio-8095-exec-1] Completed initialization in 11 ms
2025-06-12 11:13:49,299 INFO c.m.s.s.c.PendingPlanningInstructionController [http-nio-8095-exec-1] Request received for patioEditar: PendingPlanningInstructionInput.Root(prefix=PendingPlanningInstructionInput.Prefix(input=PendingPlanningInstructionInput.Input(localBusinessUnitId=87, containerNumber=tevg)))
2025-06-12 11:13:51,528 INFO c.m.s.s.s.PendingPlanningInstructionService [http-nio-8095-exec-1] Catalog IDs: {isGateOut=43083, isEmpty=43081, isContainer=48752}
2025-06-12 11:13:53,232 INFO c.m.s.s.s.PendingPlanningInstructionService [http-nio-8095-exec-1] Successfully retrieved 0 pending planning instructions
2025-06-12 11:19:54,512 INFO o.s.o.j.AbstractEntityManagerFactoryBean [SpringApplicationShutdownHook] Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-12 11:19:54,517 INFO c.z.h.HikariDataSource [SpringApplicationShutdownHook] HikariPool-1 - Shutdown initiated...
2025-06-12 11:19:54,525 INFO c.z.h.HikariDataSource [SpringApplicationShutdownHook] HikariPool-1 - Shutdown completed.
2025-06-12 11:20:02,321 INFO o.s.b.StartupInfoLogger [main] Starting BusinessApplication using Java 23.0.1 with PID 16776 (C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes started by AKA665 in C:\Users\<USER>\Documents\SD1 SDY)
2025-06-12 11:20:02,325 INFO o.s.b.SpringApplication [main] No active profile set, falling back to 1 default profile: "default"
2025-06-12 11:20:07,978 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-12 11:20:10,556 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Finished Spring Data repository scanning in 2567 ms. Found 297 JPA repository interfaces.
2025-06-12 11:20:12,884 INFO o.s.b.w.e.t.TomcatWebServer [main] Tomcat initialized with port 8095 (http)
2025-06-12 11:20:12,910 INFO o.a.j.l.DirectJDKLog [main] Initializing ProtocolHandler ["http-nio-8095"]
2025-06-12 11:20:12,920 INFO o.a.j.l.DirectJDKLog [main] Starting service [Tomcat]
2025-06-12 11:20:12,923 INFO o.a.j.l.DirectJDKLog [main] Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-12 11:20:13,075 INFO o.a.j.l.DirectJDKLog [main] Initializing Spring embedded WebApplicationContext
2025-06-12 11:20:13,078 INFO o.s.b.w.s.c.ServletWebServerApplicationContext [main] Root WebApplicationContext: initialization completed in 10635 ms
2025-06-12 11:20:15,185 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Starting...
2025-06-12 11:20:17,880 INFO c.z.h.p.HikariPool [main] HikariPool-1 - Added connection ConnectionID:1 ClientConnectionId: 576b9e22-eb8a-4067-b55d-611a9925ea6b
2025-06-12 11:20:17,885 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Start completed.
2025-06-12 11:20:17,983 INFO o.h.j.i.u.LogHelper [main] HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-12 11:20:18,109 INFO o.h.Version [main] HHH000412: Hibernate ORM core version 6.3.1.Final
2025-06-12 11:20:18,188 INFO o.h.c.i.RegionFactoryInitiator [main] HHH000026: Second-level cache disabled
2025-06-12 11:20:18,834 INFO o.s.o.j.p.SpringPersistenceUnitInfo [main] No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-12 11:20:23,117 WARN o.h.m.RootClass [main] HHH000038: Composite-id class does not override equals(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-12 11:20:23,119 WARN o.h.m.RootClass [main] HHH000039: Composite-id class does not override hashCode(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-12 11:20:27,746 INFO o.h.e.t.j.p.i.JtaPlatformInitiator [main] HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-12 11:20:27,786 INFO o.s.o.j.AbstractEntityManagerFactoryBean [main] Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-12 11:20:28,795 INFO o.s.d.j.r.q.QueryEnhancerFactory [main] Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-12 11:20:52,302 WARN o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration [main] spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-12 11:20:55,880 INFO o.s.b.a.e.w.EndpointLinksResolver [main] Exposing 1 endpoint beneath base path '/actuator'
2025-06-12 11:20:56,048 INFO o.a.j.l.DirectJDKLog [main] Starting ProtocolHandler ["http-nio-8095"]
2025-06-12 11:20:56,100 INFO o.s.b.w.e.t.TomcatWebServer [main] Tomcat started on port 8095 (http) with context path '/'
2025-06-12 11:20:56,128 INFO o.s.b.StartupInfoLogger [main] Started BusinessApplication in 54.838 seconds (process running for 55.929)
2025-06-12 11:22:10,881 INFO o.a.j.l.DirectJDKLog [http-nio-8095-exec-1] Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-12 11:22:10,882 INFO o.s.w.s.FrameworkServlet [http-nio-8095-exec-1] Initializing Servlet 'dispatcherServlet'
2025-06-12 11:22:10,888 INFO o.s.w.s.FrameworkServlet [http-nio-8095-exec-1] Completed initialization in 6 ms
2025-06-12 11:22:11,133 INFO c.m.s.s.c.PendingPlanningInstructionController [http-nio-8095-exec-1] Request received for patioEditar: PendingPlanningInstructionInput.Root(prefix=PendingPlanningInstructionInput.Prefix(input=PendingPlanningInstructionInput.Input(localBusinessUnitId=87, containerNumber=MRSU4257523)))
2025-06-12 11:22:12,737 INFO c.m.s.s.s.PendingPlanningInstructionService [http-nio-8095-exec-1] Catalog IDs: {isGateOut=43083, isEmpty=43081, isContainer=48752}
2025-06-12 11:22:13,437 INFO c.m.s.s.s.PendingPlanningInstructionService [http-nio-8095-exec-1] Found 0 pending movement instructions for container 'MRSU4257523' in yard 37
2025-06-12 11:22:13,438 INFO c.m.s.s.s.PendingPlanningInstructionService [http-nio-8095-exec-1] No pending movement instructions found, returning empty result
2025-06-12 11:23:14,714 INFO o.s.o.j.AbstractEntityManagerFactoryBean [SpringApplicationShutdownHook] Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-12 11:23:14,718 INFO c.z.h.HikariDataSource [SpringApplicationShutdownHook] HikariPool-1 - Shutdown initiated...
2025-06-12 11:23:14,727 INFO c.z.h.HikariDataSource [SpringApplicationShutdownHook] HikariPool-1 - Shutdown completed.
2025-06-12 11:23:23,010 INFO o.s.b.StartupInfoLogger [main] Starting BusinessApplication using Java 23.0.1 with PID 30256 (C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes started by AKA665 in C:\Users\<USER>\Documents\SD1 SDY)
2025-06-12 11:23:23,015 INFO o.s.b.SpringApplication [main] No active profile set, falling back to 1 default profile: "default"
2025-06-12 11:23:27,388 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-12 11:23:29,504 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Finished Spring Data repository scanning in 2104 ms. Found 297 JPA repository interfaces.
2025-06-12 11:23:31,264 INFO o.s.b.w.e.t.TomcatWebServer [main] Tomcat initialized with port 8095 (http)
2025-06-12 11:23:31,282 INFO o.a.j.l.DirectJDKLog [main] Initializing ProtocolHandler ["http-nio-8095"]
2025-06-12 11:23:31,286 INFO o.a.j.l.DirectJDKLog [main] Starting service [Tomcat]
2025-06-12 11:23:31,287 INFO o.a.j.l.DirectJDKLog [main] Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-12 11:23:31,421 INFO o.a.j.l.DirectJDKLog [main] Initializing Spring embedded WebApplicationContext
2025-06-12 11:23:31,424 INFO o.s.b.w.s.c.ServletWebServerApplicationContext [main] Root WebApplicationContext: initialization completed in 8310 ms
2025-06-12 11:23:33,276 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Starting...
2025-06-12 11:23:36,327 INFO c.z.h.p.HikariPool [main] HikariPool-1 - Added connection ConnectionID:1 ClientConnectionId: 7ebf8ea4-f3aa-4e99-9910-3311137163ce
2025-06-12 11:23:36,330 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Start completed.
2025-06-12 11:23:36,386 INFO o.h.j.i.u.LogHelper [main] HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-12 11:23:36,457 INFO o.h.Version [main] HHH000412: Hibernate ORM core version 6.3.1.Final
2025-06-12 11:23:36,504 INFO o.h.c.i.RegionFactoryInitiator [main] HHH000026: Second-level cache disabled
2025-06-12 11:23:36,862 INFO o.s.o.j.p.SpringPersistenceUnitInfo [main] No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-12 11:23:39,296 WARN o.h.m.RootClass [main] HHH000038: Composite-id class does not override equals(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-12 11:23:39,297 WARN o.h.m.RootClass [main] HHH000039: Composite-id class does not override hashCode(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-12 11:23:42,271 INFO o.h.e.t.j.p.i.JtaPlatformInitiator [main] HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-12 11:23:42,317 INFO o.s.o.j.AbstractEntityManagerFactoryBean [main] Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-12 11:23:43,426 INFO o.s.d.j.r.q.QueryEnhancerFactory [main] Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-12 11:23:59,123 WARN o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration [main] spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-12 11:24:01,756 INFO o.s.b.a.e.w.EndpointLinksResolver [main] Exposing 1 endpoint beneath base path '/actuator'
2025-06-12 11:24:01,950 INFO o.a.j.l.DirectJDKLog [main] Starting ProtocolHandler ["http-nio-8095"]
2025-06-12 11:24:02,004 INFO o.s.b.w.e.t.TomcatWebServer [main] Tomcat started on port 8095 (http) with context path '/'
2025-06-12 11:24:02,055 INFO o.s.b.StartupInfoLogger [main] Started BusinessApplication in 39.905 seconds (process running for 40.818)
2025-06-12 11:26:40,938 INFO o.s.o.j.AbstractEntityManagerFactoryBean [SpringApplicationShutdownHook] Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-12 11:26:40,949 INFO c.z.h.HikariDataSource [SpringApplicationShutdownHook] HikariPool-1 - Shutdown initiated...
2025-06-12 11:26:40,961 INFO c.z.h.HikariDataSource [SpringApplicationShutdownHook] HikariPool-1 - Shutdown completed.
