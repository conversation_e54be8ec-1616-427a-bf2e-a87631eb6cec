2025-06-12 10:56:34,838 INFO o.s.b.StartupInfoLogger [main] Starting BusinessApplication using Java 23.0.1 with PID 12792 (C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes started by AKA665 in C:\Users\<USER>\Documents\SD1 SDY)
2025-06-12 10:56:34,855 INFO o.s.b.SpringApplication [main] No active profile set, falling back to 1 default profile: "default"
2025-06-12 10:56:52,961 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-12 10:56:55,499 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Finished Spring Data repository scanning in 2507 ms. Found 297 JPA repository interfaces.
2025-06-12 10:56:58,256 INFO o.s.b.w.e.t.TomcatWebServer [main] Tomcat initialized with port 8095 (http)
2025-06-12 10:56:58,272 INFO o.a.j.l.DirectJDKLog [main] Initializing ProtocolHandler ["http-nio-8095"]
2025-06-12 10:56:58,289 INFO o.a.j.l.DirectJDKLog [main] Starting service [Tomcat]
2025-06-12 10:56:58,289 INFO o.a.j.l.DirectJDKLog [main] Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-12 10:56:58,428 INFO o.a.j.l.DirectJDKLog [main] Initializing Spring embedded WebApplicationContext
2025-06-12 10:56:58,428 INFO o.s.b.w.s.c.ServletWebServerApplicationContext [main] Root WebApplicationContext: initialization completed in 23407 ms
2025-06-12 10:57:00,361 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Starting...
2025-06-12 10:57:04,094 INFO c.z.h.p.HikariPool [main] HikariPool-1 - Added connection ConnectionID:1 ClientConnectionId: db9b7b45-8dc1-4f34-9a02-f159b4267240
2025-06-12 10:57:04,095 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Start completed.
2025-06-12 10:57:04,177 INFO o.h.j.i.u.LogHelper [main] HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-12 10:57:04,286 INFO o.h.Version [main] HHH000412: Hibernate ORM core version 6.3.1.Final
2025-06-12 10:57:04,364 INFO o.h.c.i.RegionFactoryInitiator [main] HHH000026: Second-level cache disabled
2025-06-12 10:57:04,810 INFO o.s.o.j.p.SpringPersistenceUnitInfo [main] No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-12 10:57:07,850 WARN o.h.m.RootClass [main] HHH000038: Composite-id class does not override equals(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-12 10:57:07,850 WARN o.h.m.RootClass [main] HHH000039: Composite-id class does not override hashCode(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-12 10:57:11,377 INFO o.h.e.t.j.p.i.JtaPlatformInitiator [main] HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-12 10:57:11,422 INFO o.s.o.j.AbstractEntityManagerFactoryBean [main] Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-12 10:57:12,435 INFO o.s.d.j.r.q.QueryEnhancerFactory [main] Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-12 10:57:16,207 WARN o.s.c.s.AbstractApplicationContext [main] Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'planificacionCambioZonaUseCaseImpl' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\business\core\planing\usecase\planificacion\PlanificacionCambioZonaUseCaseImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'planingConfig': Unsatisfied dependency expressed through field 'findMostAvailableFullContainerGateOutService': Error creating bean with name 'findMostAvailableFullContainerGateOutService' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\sdy\service\FindMostAvailableFullContainerGateOutService.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'movementInstructionRepository' defined in com.maersk.sd1.common.repository.MovementInstructionRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findPendingMovementInstructions(java.util.List,java.lang.String,java.lang.Integer); Reason: Validation failed for query for method public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findPendingMovementInstructions(java.util.List,java.lang.String,java.lang.Integer)
2025-06-12 10:57:16,207 INFO o.s.o.j.AbstractEntityManagerFactoryBean [main] Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-12 10:57:16,211 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Shutdown initiated...
2025-06-12 10:57:17,288 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Shutdown completed.
2025-06-12 10:57:18,306 WARN z.r.i.AsyncReporter$BoundedAsyncReporter [main] Timed out waiting for in-flight spans to send
2025-06-12 10:57:18,313 INFO o.a.j.l.DirectJDKLog [main] Stopping service [Tomcat]
2025-06-12 10:57:18,332 INFO o.s.b.a.l.ConditionEvaluationReportLogger [main] 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-06-12 10:57:18,356 ERROR o.s.b.SpringApplication [main] Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'planificacionCambioZonaUseCaseImpl' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\business\core\planing\usecase\planificacion\PlanificacionCambioZonaUseCaseImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'planingConfig': Unsatisfied dependency expressed through field 'findMostAvailableFullContainerGateOutService': Error creating bean with name 'findMostAvailableFullContainerGateOutService' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\sdy\service\FindMostAvailableFullContainerGateOutService.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'movementInstructionRepository' defined in com.maersk.sd1.common.repository.MovementInstructionRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findPendingMovementInstructions(java.util.List,java.lang.String,java.lang.Integer); Reason: Validation failed for query for method public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findPendingMovementInstructions(java.util.List,java.lang.String,java.lang.Integer)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:795)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1212)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.maersk.sd1.BusinessApplication.main(BusinessApplication.java:13)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'planingConfig': Unsatisfied dependency expressed through field 'findMostAvailableFullContainerGateOutService': Error creating bean with name 'findMostAvailableFullContainerGateOutService' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\sdy\service\FindMostAvailableFullContainerGateOutService.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'movementInstructionRepository' defined in com.maersk.sd1.common.repository.MovementInstructionRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findPendingMovementInstructions(java.util.List,java.lang.String,java.lang.Integer); Reason: Validation failed for query for method public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findPendingMovementInstructions(java.util.List,java.lang.String,java.lang.Integer)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:409)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1355)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1185)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:782)
	... 19 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'findMostAvailableFullContainerGateOutService' defined in file [C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes\com\maersk\sd1\sdy\service\FindMostAvailableFullContainerGateOutService.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'movementInstructionRepository' defined in com.maersk.sd1.common.repository.MovementInstructionRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findPendingMovementInstructions(java.util.List,java.lang.String,java.lang.Integer); Reason: Validation failed for query for method public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findPendingMovementInstructions(java.util.List,java.lang.String,java.lang.Integer)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:795)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1212)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 43 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'movementInstructionRepository' defined in com.maersk.sd1.common.repository.MovementInstructionRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findPendingMovementInstructions(java.util.List,java.lang.String,java.lang.Integer); Reason: Validation failed for query for method public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findPendingMovementInstructions(java.util.List,java.lang.String,java.lang.Integer)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1806)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:782)
	... 56 common frames omitted
Caused by: org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findPendingMovementInstructions(java.util.List,java.lang.String,java.lang.Integer); Reason: Validation failed for query for method public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findPendingMovementInstructions(java.util.List,java.lang.String,java.lang.Integer)
	at org.springframework.data.repository.query.QueryCreationException.create(QueryCreationException.java:101)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:119)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.mapMethodsToQuery(QueryExecutorMethodInterceptor.java:103)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lambda$new$0(QueryExecutorMethodInterceptor.java:92)
	at java.base/java.util.Optional.map(Optional.java:260)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.<init>(QueryExecutorMethodInterceptor.java:92)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport.getRepository(RepositoryFactorySupport.java:357)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.lambda$afterPropertiesSet$5(RepositoryFactoryBeanSupport.java:290)
	at org.springframework.data.util.Lazy.getNullable(Lazy.java:135)
	at org.springframework.data.util.Lazy.get(Lazy.java:113)
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.afterPropertiesSet(RepositoryFactoryBeanSupport.java:296)
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean.afterPropertiesSet(JpaRepositoryFactoryBean.java:132)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1802)
	... 67 common frames omitted
Caused by: java.lang.IllegalArgumentException: Validation failed for query for method public abstract java.util.List com.maersk.sd1.common.repository.MovementInstructionRepository.findPendingMovementInstructions(java.util.List,java.lang.String,java.lang.Integer)
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.validateQuery(SimpleJpaQuery.java:100)
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.<init>(SimpleJpaQuery.java:70)
	at org.springframework.data.jpa.repository.query.JpaQueryFactory.fromMethodWithQueryString(JpaQueryFactory.java:60)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$DeclaredQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:170)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateIfNotFoundQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:252)
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$AbstractQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:95)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:115)
	... 79 common frames omitted
Caused by: java.lang.IllegalArgumentException: org.hibernate.query.sqm.UnknownEntityException: Could not resolve root entity 'InstruccionMovimiento'
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:143)
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:167)
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:173)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:802)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:707)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:132)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.orm.jpa.ExtendedEntityManagerCreator$ExtendedEntityManagerInvocationHandler.invoke(ExtendedEntityManagerCreator.java:364)
	at jdk.proxy2/jdk.proxy2.$Proxy172.createQuery(Unknown Source)
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.validateQuery(SimpleJpaQuery.java:94)
	... 85 common frames omitted
Caused by: org.hibernate.query.sqm.UnknownEntityException: Could not resolve root entity 'InstruccionMovimiento'
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.resolveRootEntity(SemanticQueryBuilder.java:2012)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitRootEntity(SemanticQueryBuilder.java:1943)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitRootEntity(SemanticQueryBuilder.java:268)
	at org.hibernate.grammars.hql.HqlParser$RootEntityContext.accept(HqlParser.java:2515)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitEntityWithJoins(SemanticQueryBuilder.java:1913)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitFromClause(SemanticQueryBuilder.java:1900)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitQuery(SemanticQueryBuilder.java:1147)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitQuerySpecExpression(SemanticQueryBuilder.java:940)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitQuerySpecExpression(SemanticQueryBuilder.java:268)
	at org.hibernate.grammars.hql.HqlParser$QuerySpecExpressionContext.accept(HqlParser.java:1844)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitSimpleQueryGroup(SemanticQueryBuilder.java:925)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitSimpleQueryGroup(SemanticQueryBuilder.java:268)
	at org.hibernate.grammars.hql.HqlParser$SimpleQueryGroupContext.accept(HqlParser.java:1718)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitSelectStatement(SemanticQueryBuilder.java:442)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitStatement(SemanticQueryBuilder.java:401)
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.buildSemanticModel(SemanticQueryBuilder.java:310)
	at org.hibernate.query.hql.internal.StandardHqlTranslator.translate(StandardHqlTranslator.java:71)
	at org.hibernate.query.internal.QueryInterpretationCacheStandardImpl.createHqlInterpretation(QueryInterpretationCacheStandardImpl.java:165)
	at org.hibernate.query.internal.QueryInterpretationCacheStandardImpl.resolveHqlInterpretation(QueryInterpretationCacheStandardImpl.java:147)
	at org.hibernate.internal.AbstractSharedSessionContract.interpretHql(AbstractSharedSessionContract.java:744)
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:794)
	... 92 common frames omitted
